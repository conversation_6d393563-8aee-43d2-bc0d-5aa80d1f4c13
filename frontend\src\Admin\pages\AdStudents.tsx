import React, { useState, useEffect } from "react";
import Navbar from "../../common_components/Navbar";
import Sidebar from "../../common_components/Sidebar";
import DepartmentSelector from "../../common_components/DepartmentSelector";
import studentService, { Student } from "../../services/studentService";
import CourseMultiSelect from "../components/CourseMultiSelect";
import StudentGradesheetIntegration from "../components/StudentGradesheetIntegration";
import { useDepartments } from "../../hooks/useDepartments";
import { canEditStudent, getAcademicYearOptions } from "../../utils/academicYearUtils";

const academicYear = getAcademicYearOptions();

// Helper function to extract course IDs
const extractCourseIds = (courses: (string | any)[]): string[] => {
  if (!courses) return [];
  return courses.map((course) => {
    if (typeof course === "string") return course;
    return course._id || course.courseCode || course;
  });
};

const AdStudents: React.FC = () => {
  const { departments, departmentsLoading } = useDepartments();

  const [selectedAcademicYear, setSelectedAcademicYear] = useState(getAcademicYearOptions()[0]);
  const [selectedDept, setSelectedDept] = useState("");
  const [selectedYearSem, setSelectedYearSem] = useState("");
  const [showAddModal, setShowAddModal] = useState(false);
  const [students, setStudents] = useState<Student[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Add student form states
  const [batch, setBatch] = useState("");
  const [rollNo, setRollNo] = useState("");
  const [name, setName] = useState("");
  const [email, setEmail] = useState("");
  const [mobileNumber, setMobileNumber] = useState("");
  const [theoryCourses, setTheoryCourses] = useState<string[]>([]);
  const [labCourses, setLabCourses] = useState<string[]>([]);
  const [newStudentDiv, setNewStudentDiv] = useState("");

  // Division and batch states
  const [availableDivisions, setAvailableDivisions] = useState<string[]>([]);
  const [divisionsLoading, setDivisionsLoading] = useState(false);
  const [activeDivision, setActiveDivision] = useState("");
  const [availableBatches, setAvailableBatches] = useState<string[]>([]);
  const [batchesLoading, setBatchesLoading] = useState(false);
  const [availableYearSems, setAvailableYearSems] = useState<string[]>([]);
  const [yearSemLoading, setYearSemLoading] = useState(false);

  // Edit student states
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingStudent, setEditingStudent] = useState<Student | null>(null);
  const [showSaveConfirmModal, setShowSaveConfirmModal] = useState(false);
  const [showDeleteConfirmModal, setShowDeleteConfirmModal] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [showCSVModal, setShowCSVModal] = useState(false);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [selectedStudent, setSelectedStudent] = useState<Student | null>(null);

  // Fetch students from backend
  const fetchStudents = async () => {
    if (!selectedDept || !selectedYearSem || !selectedAcademicYear) return;
    try {
      setLoading(true);
      setError(null);
      const filters = {
        department: selectedDept,
        academicYear: selectedAcademicYear,
        currentYearSem: selectedYearSem,
        search: searchQuery.trim() || undefined,
      };
      const response = await studentService.getStudents(filters);
      setStudents(response.data);
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to fetch students");
    } finally {
      setLoading(false);
    }
  };

  // Fetch available year-semesters
  const fetchAvailableYearSems = async () => {
    try {
      const response = await studentService.getAvailableYearSemesters();
      if (response.data.length > 0 && !selectedYearSem) {
        setSelectedYearSem(response.data[0]);
      }
    } catch {
      const fallbackYearSems = [
        "FY - I", "FY - II", "SY - III", "SY - IV",
        "TY - V", "TY - VI", "LY - VII", "LY - VIII"
      ];
      if (!selectedYearSem) {
        setSelectedYearSem(fallbackYearSems[0]);
      }
    }
  };

  // Fetch available divisions for the selected department and year-sem
  const fetchAvailableDivisions = async () => {
    if (!selectedDept || !selectedYearSem) {
      console.log("Cannot fetch divisions: missing dept or yearSem", { selectedDept, selectedYearSem });
      return;
    }
    
    try {
      setDivisionsLoading(true);
      console.log("Fetching divisions for:", { selectedDept, selectedYearSem, selectedAcademicYear });
      const response = await studentService.getAvailableDivisions(
        selectedDept,
        selectedYearSem,
        selectedAcademicYear
      );
      console.log("Available divisions response:", response.data);
      setAvailableDivisions(response.data);
      
      // Set the first available division as active if no division is selected
      if (response.data.length > 0 && !activeDivision) {
        console.log("Setting default division:", response.data[0]);
        setActiveDivision(response.data[0]);
      } else if (response.data.length === 0) {
        console.log("No divisions found, clearing active division");
        setActiveDivision("");
      }
    } catch (err: any) {
      console.error("Error fetching divisions:", err);
      setError("Failed to load divisions. Using fallback options.");
      // Fallback to basic divisions if API fails
      setAvailableDivisions(["Div A", "Div B", "Div C"]);
      if (!activeDivision) {
        setActiveDivision("Div A");
      }
    } finally {
      setDivisionsLoading(false);
    }
  };

  // Fetch available batches for the selected department, year-sem, and division
  const fetchAvailableBatches = async (div: string) => {
    if (!selectedDept || !selectedYearSem || !div) return;
    
    try {
      setBatchesLoading(true);
      const response = await studentService.getAvailableBatches(
        selectedDept,
        selectedYearSem,
        div,
        selectedAcademicYear
      );
      setAvailableBatches(response.data);
      
      // Set the first available batch as selected if no batch is selected
      if (response.data.length > 0 && !batch) {
        setBatch(response.data[0]);
      }
    } catch (err: any) {
      console.error("Error fetching batches:", err);
      // Fallback to division-specific batches if API fails
      let fallbackBatches = ["A1", "A2", "B1", "B2", "C1", "C2"];
      
      // Provide division-specific fallback batches
      if (div === "Div A") {
        fallbackBatches = ["A1", "A2"];
      } else if (div === "Div B") {
        fallbackBatches = ["B1", "B2"];
      } else if (div === "Div C") {
        fallbackBatches = ["C1", "C2"];
      }
      
      setAvailableBatches(fallbackBatches);
      if (!batch) {
        setBatch(fallbackBatches[0]);
      }
    } finally {
      setBatchesLoading(false);
    }
  };

  // Initialize default values when departments load
  // Load initial data on component mount
  useEffect(() => {
    console.log("Component mounted, fetching initial data");
    fetchAvailableYearSems();
  }, []);

  // Set default department when departments are loaded
  useEffect(() => {
    if (departments.length > 0 && !selectedDept) {
      setSelectedDept(departments[0]);
    }
  }, [departments, selectedDept]);

  // Fetch divisions when department or year-sem changes
  useEffect(() => {
    if (selectedDept && selectedYearSem) {
      fetchAvailableDivisions();
    }
  }, [selectedDept, selectedYearSem, selectedAcademicYear]);

  // Ensure divisions are loaded when add modal opens
  useEffect(() => {
    if (showAddModal && availableDivisions.length === 0 && selectedDept && selectedYearSem) {
      fetchAvailableDivisions();
    }
  }, [showAddModal, availableDivisions.length, selectedDept, selectedYearSem]);

  useEffect(() => {
    if (selectedDept && selectedYearSem && selectedAcademicYear) {
      fetchStudents();
    }
  }, [selectedDept, selectedYearSem, selectedAcademicYear, searchQuery]);

  const handleAddStudent = async () => {
    try {
      setLoading(true);
      if (!name || !email || !selectedDept || !selectedAcademicYear || !selectedYearSem || !rollNo) {
        alert("Please fill all required fields");
        return;
      }
      const newStudentData = {
        name,
        email,
        rollNumber: rollNo,
        mobileNumber,
        progDept: selectedDept,
        academicYear: selectedAcademicYear,
        currentYearSem: selectedYearSem,
        div: newStudentDiv,
        batch,
        enrolledTheoryCourses: theoryCourses,
        enrolledLabCourses: labCourses,
      };
      await studentService.createStudent(newStudentData);
      setBatch("");
      setRollNo("");
      setName("");
      setEmail("");
      setMobileNumber("");
      setTheoryCourses([]);
      setLabCourses([]);
      setNewStudentDiv("");
      setShowAddModal(false);
      await fetchStudents();
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to add student");
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateStudent = async () => {
    if (!editingStudent || !editingStudent._id) return;
    try {
      setLoading(true);
      const updateData = {
        name: editingStudent.name,
        email: editingStudent.email,
        rollNumber: editingStudent.rollNumber,
        mobileNumber: editingStudent.mobileNumber,
        div: editingStudent.div,
        batch: editingStudent.batch,
        enrolledTheoryCourses: editingStudent.enrolledTheoryCourses,
        enrolledLabCourses: editingStudent.enrolledLabCourses,
      };
      await studentService.updateStudent(editingStudent._id, updateData);
      setShowEditModal(false);
      setShowSaveConfirmModal(false);
      setEditingStudent(null);
      await fetchStudents();
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to update student");
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteStudent = async () => {
    if (!editingStudent || !editingStudent._id) return;
    try {
      setLoading(true);
      await studentService.deleteStudent(editingStudent._id);
      setShowEditModal(false);
      setEditingStudent(null);
      await fetchStudents();
    } catch (err: any) {
      setError(err.response?.data?.message || "Failed to delete student");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <Navbar role="admin" />
      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar role="admin" />
        </div>
        <main className="flex-1 py-6 px-5 md:px-7 bg-white overflow-auto">
          <h1 className="text-[27px] font-bold text-[#ED1C24] text-center mb-7">Students</h1>

          {/* Filters Section */}
          <div className="flex flex-col md:flex-row md:justify-between md:items-start gap-4">
            <div className="flex flex-col md:flex-row gap-4 flex-1">
              <div className="flex flex-col w-full md:w-[370px]">
                <DepartmentSelector
                  label="Department:"
                  value={selectedDept}
                  onChange={setSelectedDept}
                  departments={departments}
                  loading={departmentsLoading}
                  className="border px-4 py-1.5 rounded w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                  placeholder="Select a department"
                />
              </div>
              <div className="flex flex-col w-full md:w-[130px]">
                <label className="text-[16px] font-medium mb-1">Year - Sem:</label>
                <select
                  className="border px-4 py-1.5 rounded w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                  value={selectedYearSem}
                  onChange={(e) => setSelectedYearSem(e.target.value)}
                  disabled={yearSemLoading}
                >
                  {yearSemLoading ? (
                    <option>Loading year-semesters...</option>
                  ) : availableYearSems.length === 0 ? (
                    <option>No year-semesters available</option>
                  ) : (
                    availableYearSems.map((ys: string, index: number) => (
                      <option key={index} value={ys}>
                        {ys}
                      </option>
                    ))
                  )}
                </select>
              </div>
            </div>
            <div className="flex flex-col items-end gap-2 w-full md:w-auto">
              <div className="flex flex-col w-full md:w-[160px]">
                <label className="text-[16px] font-medium mb-1">Academic Year:</label>
                <select
                  className="border px-4 py-1.5 rounded w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                  value={selectedAcademicYear}
                  onChange={(e) => setSelectedAcademicYear(e.target.value)}
                >
                  {academicYear.map((ay, index) => (
                    <option key={index} value={ay}>
                      {ay}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>

          <hr className="mt-4 border-t border-2 border-gray-300" />

          {/* Search and Actions */}
          <div className="flex flex-col md:flex-row justify-between items-end mb-10 gap-4">
            <div className="flex flex-col w-full md:w-[500px] md:mt-7">
              <input
                type="text"
                className="border px-4 py-1.5 rounded-xl w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                placeholder="Search by Student Roll Number or Name"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={() => setShowCSVModal(true)}
                className="bg-[#28a745] text-white px-4 py-2 rounded-xl hover:bg-[#218838]"
                disabled={loading}
              >
                Import CSV
              </button>
              <button
                onClick={async () => {
                  // Validate that required dropdowns are selected
                  if (!selectedDept) {
                    alert("Please select a department first");
                    return;
                  }
                  if (!selectedAcademicYear) {
                    alert("Please select an academic year first");
                    return;
                  }
                  if (!selectedYearSem) {
                    alert("Please select a year-semester first");
                    return;
                  }
                  
                  // Ensure divisions are loaded
                  if (availableDivisions.length === 0) {
                    await fetchAvailableDivisions();
                  }
                  
                  // Initialize form with default values
                  const defaultDivision = availableDivisions[0] || "Div A";
                  setNewStudentDiv(defaultDivision);
                  setBatch("");
                  setRollNo("");
                  setName("");
                  setEmail("");
                  setMobileNumber("");
                  setTheoryCourses([]);
                  setLabCourses([]);
                  setShowAddModal(true);
                  
                  // Fetch batches for the selected division
                  await fetchAvailableBatches(defaultDivision);
                }}
                className="bg-[#ED1C24] text-white px-4 py-2 rounded-xl hover:bg-[#c4171d] mt-0"
                disabled={loading}
              >
                + Add Student
              </button>
            </div>
          </div>

          {/* Table */}
          <table className="min-w-full mt-6 border text-left text-sm">
            <thead className="bg-gray-100">
              <tr>
                <th className="px-4 py-2 border">Sr.No</th>
                <th className="px-4 py-2 border">Batch</th>
                <th className="px-4 py-2 border">Roll No</th>
                <th className="px-4 py-2 border">Name</th>
                <th className="px-4 py-2 border">Theory</th>
                <th className="px-4 py-2 border">Lab</th>
                <th className="px-4 py-2 border">Action</th>
              </tr>
            </thead>
            <tbody>
              {students.length > 0 ? students.map((student, idx) => (
                <tr key={student._id}>
                  <td className="px-4 py-2 border">{idx + 1}</td>
                  <td className="px-4 py-2 border">{student.batch}</td>
                  <td className="px-4 py-2 border">{student.rollNumber}</td>
                  <td className="px-4 py-2 border">{student.name}</td>
                  <td className="px-4 py-2 border">{student.enrolledTheoryCourses?.length || 0}</td>
                  <td className="px-4 py-2 border">{student.enrolledLabCourses?.length || 0}</td>
                  <td className="px-4 py-2 border">
                    <button
                      className="text-blue-600"
                      onClick={() => {
                        const theoryIds = extractCourseIds(student.enrolledTheoryCourses || []);
                        const labIds = extractCourseIds(student.enrolledLabCourses || []);
                        setEditingStudent({ ...student, enrolledTheoryCourses: theoryIds, enrolledLabCourses: labIds });
                        setShowEditModal(true);
                      }}
                    >
                      Edit
                    </button>
                  </td>
                </tr>
              )) : (
                <tr>
                  <td colSpan={7} className="text-center py-4">No students found</td>
                </tr>
              )}
            </tbody>
          </table>

          {/* Add Modal */}
          {showAddModal && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
              <div className="bg-white rounded-lg w-full max-w-2xl max-h-[93vh] overflow-y-auto p-6 shadow-lg">
                <h2 className="text-xl font-bold text-red-600 mb-4">Add Student</h2>
                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Department:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full bg-gray-100"
                        value={selectedDept}
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Year - Sem:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full bg-gray-100"
                        value={selectedYearSem}
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Division:</label>
                      <input
                        type="text"
                        className="border px-2 py-1.5 rounded w-full"
                        value={newStudentDiv}
                        onChange={(e) => {
                          setNewStudentDiv(e.target.value);
                          setBatch(""); // Clear batch when division changes
                        }}
                        placeholder="Enter division (e.g., Div A, Div B)"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Batch:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={batch}
                        onChange={(e) => setBatch(e.target.value)}
                        placeholder="Enter batch (e.g., A1, B2, C1)"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Roll No:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={rollNo}
                        onChange={(e) => setRollNo(e.target.value)}
                        placeholder="e.g., D22CO001"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Mobile Number:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={mobileNumber}
                        onChange={(e) => setMobileNumber(e.target.value)}
                        placeholder="10-digit mobile number"
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Name:</label>
                    <input
                      type="text"
                      className="border px-3 py-1.5 rounded w-full"
                      value={name}
                      onChange={(e) => setName(e.target.value)}
                      placeholder="Full name"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Email:</label>
                    <input
                      type="email"
                      className="border px-3 py-1.5 rounded w-full"
                      value={email}
                      onChange={(e) => setEmail(e.target.value)}
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <CourseMultiSelect
                      label="Theory Courses"
                      type="theory"
                      selectedCourses={theoryCourses}
                      onChange={setTheoryCourses}
                      department={selectedDept}
                      academicYear={selectedAcademicYear}
                      currentYearSem={selectedYearSem}
                      placeholder="Select theory courses..."
                    />

                    <CourseMultiSelect
                      label="Lab Courses"
                      type="lab"
                      selectedCourses={labCourses}
                      onChange={setLabCourses}
                      department={selectedDept}
                      academicYear={selectedAcademicYear}
                      currentYearSem={selectedYearSem}
                      placeholder="Select lab courses..."
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-6 gap-3">
                  <button
                    className="px-4 py-1.5 border rounded hover:bg-gray-100"
                    onClick={() => setShowAddModal(false)}
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-[#ED1C24] text-white px-4 py-1.5 rounded hover:bg-[#c4171d]"
                    onClick={handleAddStudent}
                    disabled={loading}
                  >
                    {loading ? "Adding..." : "Add Student"}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Edit Modal */}
          {showEditModal && editingStudent && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
              <div className="bg-white rounded-lg w-full max-w-2xl max-h-[93vh] overflow-y-auto p-6 shadow-lg">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-xl font-bold text-red-600">Edit Student</h2>
                  <button
                    className="text-red-600 font-medium hover:underline"
                    onClick={() => setShowDeleteConfirmModal(true)}
                  >
                    Delete
                  </button>
                </div>

                <div className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label className="text-sm font-medium">Department:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full bg-gray-100"
                        value={editingStudent.progDept}
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Year - Sem:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full bg-gray-100"
                        value={editingStudent.currentYearSem}
                        readOnly
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Division:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={editingStudent.div}
                        onChange={(e) => {
                          setEditingStudent({ ...editingStudent, div: e.target.value });
                        }}
                        placeholder="Enter division (e.g., Div A, Div B)"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Batch:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={editingStudent.batch}
                        onChange={(e) =>
                          setEditingStudent({ ...editingStudent, batch: e.target.value })
                        }
                        placeholder="Enter batch (e.g., A1, B2, C1)"
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Roll No:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={editingStudent.rollNumber}
                        onChange={(e) =>
                          setEditingStudent({ ...editingStudent, rollNumber: e.target.value })
                        }
                      />
                    </div>
                    <div>
                      <label className="text-sm font-medium">Mobile Number:</label>
                      <input
                        type="text"
                        className="border px-3 py-1.5 rounded w-full"
                        value={editingStudent.mobileNumber || ""}
                        onChange={(e) =>
                          setEditingStudent({ ...editingStudent, mobileNumber: e.target.value })
                        }
                      />
                    </div>
                  </div>

                  <div>
                    <label className="text-sm font-medium">Name:</label>
                    <input
                      type="text"
                      className="border px-3 py-1.5 rounded w-full"
                      value={editingStudent.name}
                      onChange={(e) =>
                        setEditingStudent({ ...editingStudent, name: e.target.value })
                      }
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Email:</label>
                    <input
                      type="email"
                      className="border px-3 py-1.5 rounded w-full"
                      value={editingStudent.email}
                      onChange={(e) =>
                        setEditingStudent({ ...editingStudent, email: e.target.value })
                      }
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <CourseMultiSelect
                      label="Theory Courses"
                      type="theory"
                      selectedCourses={(editingStudent.enrolledTheoryCourses as string[]) || []}
                      onChange={(courses) =>
                        setEditingStudent({ ...editingStudent, enrolledTheoryCourses: courses })
                      }
                      department={editingStudent.progDept}
                      academicYear={editingStudent.academicYear}
                      currentYearSem={editingStudent.currentYearSem}
                      placeholder="Select theory courses..."
                    />

                    <CourseMultiSelect
                      label="Lab Courses"
                      type="lab"
                      selectedCourses={(editingStudent.enrolledLabCourses as string[]) || []}
                      onChange={(courses) =>
                        setEditingStudent({ ...editingStudent, enrolledLabCourses: courses })
                      }
                      department={editingStudent.progDept}
                      academicYear={editingStudent.academicYear}
                      currentYearSem={editingStudent.currentYearSem}
                      placeholder="Select lab courses..."
                    />
                  </div>
                </div>

                <div className="flex justify-end mt-6 gap-3">
                  <button
                    className="px-4 py-1.5 border rounded bg-gray-200 hover:bg-gray-300"
                    onClick={() => setShowEditModal(false)}
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-[#ED1C24] text-white px-4 py-1.5 rounded hover:bg-[#c4171d]"
                    onClick={() => setShowSaveConfirmModal(true)}
                    disabled={loading}
                  >
                    Save Changes
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Delete Confirmation Modal */}
          {showDeleteConfirmModal && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
              <div className="bg-white rounded-lg w-full max-w-sm p-6 shadow-lg">
                <h2 className="text-lg font-bold text-red-600 mb-4">Confirm Delete</h2>
                <p className="mb-4">
                  Are you sure you want to delete this student? This action cannot be undone.
                </p>
                <div className="flex justify-end gap-3">
                  <button
                    className="px-4 py-1.5 border rounded hover:bg-gray-100"
                    onClick={() => setShowDeleteConfirmModal(false)}
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-[#ED1C24] text-white px-4 py-1.5 rounded hover:bg-[#c4171d]"
                    onClick={handleDeleteStudent}
                    disabled={loading}
                  >
                    {loading ? "Deleting..." : "Delete"}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Save Confirmation Modal */}
          {showSaveConfirmModal && (
            <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-30 z-50">
              <div className="bg-white rounded-lg w-full max-w-sm p-6 shadow-lg">
                <h2 className="text-lg font-bold text-red-600 mb-4">Confirm Save</h2>
                <p className="mb-4">
                  You have made changes to this student's details. Do you want to save the changes?
                </p>
                <div className="flex justify-end gap-3">
                  <button
                    className="px-4 py-1.5 border rounded hover:bg-gray-100"
                    onClick={() => setShowSaveConfirmModal(false)}
                    disabled={loading}
                  >
                    Cancel
                  </button>
                  <button
                    className="bg-[#ED1C24] text-white px-4 py-1.5 rounded hover:bg-[#c4171d]"
                    onClick={handleUpdateStudent}
                    disabled={loading}
                  >
                    {loading ? "Saving..." : "Save"}
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Student Detail Modal */}
          {showDetailModal && selectedStudent && (
            <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
              <div className="bg-white p-6 rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
                <div className="flex justify-between items-center mb-6">
                  <h2 className="text-2xl font-bold text-gray-800">
                    Student Details: {selectedStudent.name}
                  </h2>
                  <button
                    className="text-gray-500 hover:text-gray-700 text-2xl"
                    onClick={() => {
                      setShowDetailModal(false);
                      setSelectedStudent(null);
                    }}
                  >
                    ×
                  </button>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Basic Information */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold mb-3 text-gray-700">Basic Information</h3>
                    <div className="space-y-2">
                      <p>
                        <span className="font-medium">Roll Number:</span>{" "}
                        {selectedStudent.rollNumber}
                      </p>
                      <p>
                        <span className="font-medium">Name:</span> {selectedStudent.name}
                      </p>
                      <p>
                        <span className="font-medium">Email:</span> {selectedStudent.email || "N/A"}
                      </p>
                      <p>
                        <span className="font-medium">Mobile:</span>{" "}
                        {selectedStudent.mobileNumber || "N/A"}
                      </p>
                      <p>
                        <span className="font-medium">Academic Year:</span>{" "}
                        {selectedStudent.academicYear}
                      </p>
                      <p>
                        <span className="font-medium">Department:</span> {selectedStudent.progDept}
                      </p>
                      <p>
                        <span className="font-medium">Year/Sem:</span>{" "}
                        {selectedStudent.currentYearSem}
                      </p>
                      <p>
                        <span className="font-medium">Division:</span> {selectedStudent.div}
                      </p>
                      <p>
                        <span className="font-medium">Batch:</span> {selectedStudent.batch}
                      </p>
                    </div>
                  </div>

                  {/* Course Information */}
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <h3 className="text-lg font-semibold mb-3 text-gray-700">Enrolled Courses</h3>
                    <div className="space-y-2">
                      {selectedStudent.enrolledTheoryCourses &&
                      selectedStudent.enrolledTheoryCourses.length > 0 ? (
                        <div>
                          <p className="font-medium text-blue-600">Theory Courses:</p>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {selectedStudent.enrolledTheoryCourses.map(
                              (course: any, index: number) => (
                                <div
                                  key={index}
                                  className="bg-blue-100 text-blue-800 px-3 py-2 rounded text-xs border"
                                >
                                  <div className="font-medium">
                                    {typeof course === "string"
                                      ? course
                                      : course.courseName || course.courseCode || course}
                                  </div>
                                  <div className="text-xs opacity-75 mt-1">
                                    Code: {typeof course === "string" 
                                      ? course 
                                      : course.courseCode || course._id || course}
                                  </div>
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      ) : (
                        <p className="text-gray-500">No theory courses enrolled</p>
                      )}

                      {selectedStudent.enrolledLabCourses &&
                      selectedStudent.enrolledLabCourses.length > 0 ? (
                        <div className="mt-3">
                          <p className="font-medium text-green-600">Lab Courses:</p>
                          <div className="flex flex-wrap gap-2 mt-1">
                            {selectedStudent.enrolledLabCourses.map(
                              (course: any, index: number) => (
                                <div
                                  key={index}
                                  className="bg-green-100 text-green-800 px-3 py-2 rounded text-xs border"
                                >
                                  <div className="font-medium">
                                    {typeof course === "string"
                                      ? course
                                      : course.courseName || course.courseCode || course}
                                  </div>
                                  <div className="text-xs opacity-75 mt-1">
                                    Code: {typeof course === "string" 
                                      ? course 
                                      : course.courseCode || course._id || course}
                                  </div>
                                </div>
                              )
                            )}
                          </div>
                        </div>
                      ) : (
                        <p className="text-gray-500 mt-3">No lab courses enrolled</p>
                      )}
                    </div>
                  </div>
                </div>

                {/* Gradesheet Integration */}
                <div className="mt-6">
                  <h3 className="text-lg font-semibold mb-3 text-gray-700">Gradesheet Status</h3>
                  {selectedStudent._id ? (
                    <StudentGradesheetIntegration
                      student={{
                        _id: selectedStudent._id,
                        name: selectedStudent.name,
                        rollNumber: selectedStudent.rollNumber,
                        progDept: selectedStudent.progDept,
                        academicYear: selectedStudent.academicYear,
                        currentYearSem: selectedStudent.currentYearSem,
                        div: selectedStudent.div,
                        enrolledTheoryCourses: extractCourseIds(
                          selectedStudent.enrolledTheoryCourses || []
                        ),
                        enrolledLabCourses: extractCourseIds(
                          selectedStudent.enrolledLabCourses || []
                        ),
                      }}
                    />
                  ) : (
                    <p className="text-gray-500">Student ID not available</p>
                  )}
                </div>
              </div>
            </div>
          )}

          {/* CSV Import Modal - Temporarily disabled */}
          {/* {showCSVModal && (
            <CSVImportModal
              isOpen={showCSVModal}
              onClose={() => setShowCSVModal(false)}
              onImportSuccess={() => {
                setShowCSVModal(false);
                fetchStudents();
              }}
              defaultData={{
                progDept: selectedDept,
                academicYear: selectedAcademicYear,
                currentYearSem: selectedYearSem,
                div: activeDivision,
              }}
            />
          )} */}
        </main>
      </div>
    </div>
  );
};

export default AdStudents;
