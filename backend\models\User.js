import mongoose from "mongoose";

// const principalSchema = new mongoose.Schema({
//     name: { type: String, required: true, trim: true },
//     email: { type: String, required: true, unique: true, lowercase: true },
//     passwordHash:  { type: String, required: true },
//     contactNumber: { type: String },
//     profilePicture:{ type: String },          // URL
//     editGradesheet : {type: Boolean, required : true}
// });

const FACULTY_ROLES = [
  "Faculty",
  "Course Coordinator",
  "CIS Coordinator",
  "HOD",
  "Principal",
  "Exam Cell",
  "Admin",
];

const userSchema = new mongoose.Schema(
  {
    // Basic Info
    name: { type: String, required: true },
    username: { type: String, unique: true, sparse: true }, // Allow login with username or email
    email: { type: String, required: true, unique: true },
    mobileNumber: String,
    dateOfBirth: Date,
    passwordHash: String,

    // Role and Department
    role: {
      type: String,
      enum: [
        "Faculty",
        "Course Coordinator",
        "CIS Coordinator",
        "<PERSON><PERSON>",
        "Principal",
        "<PERSON>am Cell",
        "Admin",
        "Student",
      ],
      required: true,
    },
    department: String, //S A H
    progDept: String, // CS,
    academicYear: String, //Can be Null for Faculty
    currentYearSem: String, // NULL FOR FACULTY

    // Optional permissions or tags for finer control
    permissions: [String],

    // Status field for enable/disable functionality
    isActive: { type: Boolean, default: true },

    /* ───────────  Faculty-only  ──────────── */
    facultyID: {
      type: String,
      unique: true,
      required() {
        // required only for faculty-type roles
        return FACULTY_ROLES.includes(this.role);
      },
      validate: {
        // must be empty if the role is Student
        validator(v) {
          return this.role === "Student" ? !v : true;
        },
        message: "facultyID must be blank for students",
      },
      sparse: true, // let Mongo ignore nulls for the unique index
    },

    /* ───────────  Course Coordinator specific fields  ──── */
    courseCode: {
      type: String,
      required() {
        return this.role === "Course Coordinator";
      },
    },
    courseName: {
      type: String,
      required() {
        return this.role === "Course Coordinator";
      },
    },
    yearSem: {
      type: String,
      required() {
        return this.role === "Course Coordinator";
      },
    },

    /* ───────────  Faculty profile refs  ──── */
    facultyProfile: {
      teachingOfferings: [{ type: mongoose.Schema.Types.ObjectId, ref: "CourseOffering" }],
      coordinatingOfferings: [{ type: mongoose.Schema.Types.ObjectId, ref: "CourseOffering" }],
    },

    /* ───────────  Student-only  ──────────── */

    rollNumber: {
      type: String,
      unique: true,
      required() {
        // required only for Student
        return this.role === "Student";
      },
      sparse: true,
    },

    // Student-only fields (will be empty for staff)
    div: String,
    batch: String,
    enrolledTheoryCourses: [{ type: mongoose.Schema.Types.ObjectId, ref: "courseDetails" }],
    enrolledLabCourses: [{ type: mongoose.Schema.Types.ObjectId, ref: "courseDetails" }],
  },
  { timestamps: true }
);

export default mongoose.model("Users", userSchema);
