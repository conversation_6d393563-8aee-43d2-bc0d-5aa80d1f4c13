import React, { useState } from "react";
import EditAddFacultyModal from "./EditAddFaculty";
import facultyService from "../../services/facultyService";

export interface Faculty {
  id: string;
  _id?: string; // MongoDB ID for backend operations
  name: string;
  dob: string;
  email: string;
  mobile: string;
  department: string;
  role?: string;
  username: string;
  password: string;
}

interface Props {
  getNextFacultyId: (role?: string) => string;
  generateSecurePassword: () => string;
  searchQuery: string;
  onSearchQueryChange: (value: string) => void;
  facultyData: Faculty[];
  setFacultyData: React.Dispatch<React.SetStateAction<Faculty[]>>;
  disabledIds: string[];
  onDisableFaculty: (id: string, enable: boolean) => void;
  onDeleteFaculty: (id: string) => void;
  departments: string[];
  isLoading?: boolean;
}

const FacultySection: React.FC<Props> = ({
  getNextFacultyId,
  generateSecurePassword,
  searchQuery,
  onSearchQueryChange,
  facultyData,
  setFacultyData,
  disabledIds,
  onDisableFaculty,
  onDeleteFaculty,
  departments,
  isLoading = false,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isEditMode, setIsEditMode] = useState(false);
  const [editingFaculty, setEditingFaculty] = useState<Faculty | null>(null);
  const [enableConfirmId, setEnableConfirmId] = useState<string | null>(null);

  const handleOpenModal = () => {
    const defaultRole = "Faculty";
    const newId = getNextFacultyId(defaultRole);
    const password = generateSecurePassword();

    setEditingFaculty({
      id: newId,
      name: "",
      dob: "",
      email: "",
      mobile: "",
      department: departments[0],
      role: defaultRole,
      username: "",
      password, // ✅ now filled
    });
    setIsEditMode(false);
    setIsModalOpen(true);
  };

  const handleEditFaculty = (f: Faculty) => {
    setEditingFaculty({ ...f });
    setIsEditMode(true);
    setIsModalOpen(true);
  };

  const handleSaveFaculty = async (facultyToSave: Faculty) => {
    try {
      if (isEditMode && editingFaculty) {
        // Update existing faculty - use MongoDB _id for backend operations
        const updateData = {
          name: facultyToSave.name,
          username: facultyToSave.username,
          email: facultyToSave.email,
          mobileNumber: facultyToSave.mobile,
          department: facultyToSave.department,
          progDept: facultyToSave.department,
          role: facultyToSave.role || "Faculty",
          password: facultyToSave.password,
          dateOfBirth: facultyToSave.dob
        };
        
        // Use _id for backend operations, fallback to id if _id not available
        const backendId = editingFaculty._id || editingFaculty.id;
        await facultyService.updateFaculty(backendId, updateData);

        // Update local state
        setFacultyData((prev) =>
          prev.map((fac) => (fac.id === editingFaculty.id ? facultyToSave : fac))
        );
      } else {
        // Create new faculty
        const createData = {
          name: facultyToSave.name,
          facultyID: facultyToSave.id,
          email: facultyToSave.email,
          mobileNumber: facultyToSave.mobile,
          department: facultyToSave.department,
          progDept: facultyToSave.department,
          role: facultyToSave.role || "Faculty",
          username: facultyToSave.username,
          password: facultyToSave.password,
          dateOfBirth: facultyToSave.dob
        };

        // Call backend API to create faculty
        const createdFaculty = await facultyService.createFaculty(createData);
        
        // Transform backend response to match frontend format
        const transformedFaculty: Faculty = {
          id: createdFaculty.facultyID,
          _id: createdFaculty._id,
          name: createdFaculty.name,
          dob: createdFaculty.dateOfBirth ? new Date(createdFaculty.dateOfBirth).toLocaleDateString('en-GB') : facultyToSave.dob,
          mobile: createdFaculty.mobileNumber?.replace(/^\+91\s*/, '') || facultyToSave.mobile,
          email: createdFaculty.email || facultyToSave.email,
          department: createdFaculty.progDept || createdFaculty.department || facultyToSave.department,
          username: facultyToSave.username,
          password: facultyToSave.password,
        };

        // Update local state
        setFacultyData((prev) => [...prev, transformedFaculty]);

        // Show success message with email notification
        alert(`Faculty created successfully! Welcome email with login credentials has been sent to ${createdFaculty.email}`);
      }
      setIsModalOpen(false);
    } catch (error) {
      console.error("Error saving faculty:", error);
      alert("Error saving faculty: " + (error as Error).message);
    }
  };

  const filteredData = facultyData.filter(
    (f) =>
      f.id.toLowerCase().includes(searchQuery.toLowerCase()) ||
      f.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleEnableFaculty = async (id: string) => {
    try {
      // Find the faculty to get their backend ID
      const faculty = facultyData.find(f => f.id === id);
      if (!faculty) return;

      // Use _id for backend operations, fallback to id if _id not available
      const backendId = faculty._id || faculty.id;
      await facultyService.toggleFacultyStatus(backendId, true);

      onDisableFaculty(id, true);
      setEnableConfirmId(null);
    } catch (error) {
      console.error("Error enabling faculty:", error);
      alert("Error enabling faculty: " + (error as Error).message);
    }
  };

  return (
    <div>
      <div className="flex justify-between flex-col sm:flex-row items-start sm:items-center gap-3 mb-5 px-2">
        <input
          type="text"
          placeholder="Search by Faculty ID or Name"
          className="border-2 px-6 py-1.5 rounded-2xl w-full sm:w-[460px] shadow-[0_1px_3px_rgba(0,0,0,0.1)]"
          value={searchQuery}
          onChange={(e) => onSearchQueryChange(e.target.value)}
        />
        <button
          onClick={handleOpenModal}
          className="bg-[#ED1C24] text-[15px] text-white px-4 py-2 rounded-lg font-medium hover:bg-red-800"
        >
          Add Faculty
        </button>
      </div>

      <div className="overflow-x-auto border rounded-lg">
        <table className="w-full bg-white text-center text-[15px]">
          <thead className="bg-[#F0F0F0] font-medium">
            <tr>
              <th className="border-2 px-4 py-3 w-[7%]">Sr. No.</th>
              <th className="border-2 px-4 py-3 w-[12%]">Faculty ID</th>
              <th className="border-2 px-4 py-3 w-[22%]">Name</th>
              <th className="border-2 px-4 py-3 w-[13%]">Mobile No.</th>
              <th className="border-2 px-4 py-3 w-[21%]">Email ID</th>
              <th className="border-2 px-4 py-3 w-[15%]">Username</th>
              <th className="border-2 px-4 py-3 w-[10%]">Action</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={7} className="py-6 text-gray-500 text-center">
                  Loading faculty data...
                </td>
              </tr>
            ) : filteredData.length === 0 ? (
              <tr>
                <td colSpan={7} className="py-6 text-gray-500">
                  No faculty found.
                </td>
              </tr>
            ) : (
              filteredData.map((f, i) => {
                const isDisabled = disabledIds.includes(f.id);
                return (
                  <tr key={f.id} className={isDisabled ? "opacity-40" : ""}>
                    <td className="border px-4 py-3">{i + 1}</td>
                    <td className="border px-4 py-3">{f.id}</td>
                    <td className="border px-6 py-3 text-left">{f.name}</td>
                    <td className="border px-4 py-3">+91 {f.mobile}</td>
                    <td className="border px-4 py-3 text-left">{f.email}</td>
                    <td className="border px-4 py-3">
                      {f.username || (
                        <span className="text-yellow-600 font-medium">Not assigned</span>
                      )}
                    </td>
                    <td className="border px-4 py-3">
                      {isDisabled ? (
                        <button
                          onClick={() => setEnableConfirmId(f.id)}
                          className="text-green-600 hover:underline font-bold"
                        >
                          Enable
                        </button>
                      ) : (
                        <button
                          onClick={() => handleEditFaculty(f)}
                          className="text-[#ED1C24] hover:underline font-bold"
                        >
                          {f.username ? "Edit" : "Assign"}
                        </button>
                      )}
                    </td>
                  </tr>
                );
              })
            )}
          </tbody>
        </table>
      </div>

      {isModalOpen && editingFaculty && (
        <EditAddFacultyModal
          faculty={editingFaculty}
          onClose={() => setIsModalOpen(false)}
          onSave={handleSaveFaculty}
          onDisable={(id) => {
            onDisableFaculty(id, false);
            setIsModalOpen(false);
            setEnableConfirmId(null);
          }}
          onDelete={onDeleteFaculty}
          isEditMode={isEditMode}
          departments={departments}
        />
      )}

      {enableConfirmId && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <p className="mb-4 text-lg">
              Are you sure you want to <strong>enable</strong>{" "}
              <span className="text-[#ED1C24]">
                {facultyData.find((f) => f.id === enableConfirmId)?.name}
              </span>
              ?
            </p>
            <div className="flex justify-center gap-4">
              <button
                onClick={() => setEnableConfirmId(null)}
                className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
              <button
                onClick={() => handleEnableFaculty(enableConfirmId)}
                className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600"
              >
                Yes, Enable
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default FacultySection;
