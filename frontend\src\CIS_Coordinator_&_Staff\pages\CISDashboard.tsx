// Imports
import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "../../common_components/Navbar";
import Sidebar from "../../common_components/Sidebar";
import CISDashboardCard from "../components/CISCard";
import AddCourse from "../components/AddCourse";
import { useDepartments } from "../../hooks/useDepartments";
import axios from "../../utils/axios";
import { academicYearService } from "../../services/academicYearService";

const tabs = ["courses", "faculties"] as const;
type CourseType = "theory" | "lab";

interface Base {
  courseId: string;
  coursename: string;
  coursecode: string;
  year: string;
  semester: string;
  coursecoordinator: string[];
  assignedFaculty: Record<string, string[]>;
  academicyear: string;
}
type Course =
  | (Base & { divs: string[]; assignedFaculty: Record<string, string[]>; type: "theory" })
  | (Base & { batches: string[]; assignedFaculty: Record<string, string[]>; type: "lab" });

interface FormFields {
  coursename: string;
  coursecode: string;
  yearSemester: string;
  coursecoordinator: string[];
  academicyear: string;
  numDivs: number;
  numBatches: number;
  divs: string[];
  batches: string[];
  divisionBatches?: Record<string, string[]>;
  assignedFaculty: Record<string, string[]>;
  divBatchCounts?: Record<string, number>;
  divisionStudents?: Record<string, string[]>;
}

interface FetchedCourse {
  _id: string;
  courseName: string;
  courseCode: string;
  academicYear: string;
  yearSem: string;
  type: "theory" | "lab";
  courseCoordinators: { facultyID: string; name: string }[];
  divisions: { name: string; faculty: { facultyID: string }[] }[];
  batches: { name: string; faculty: { facultyID: string }[] }[];
}

const initialForm: FormFields = {
  coursename: "",
  coursecode: "",
  yearSemester: "",
  coursecoordinator: [],
          academicyear: "", // Will be set when academic years load
  numDivs: 1,
  numBatches: 1,
  divs: ["A"],
  batches: ["A1"],
  assignedFaculty: {},
  divBatchCounts: { A: 1 },
  divisionBatches: { A: ["A1"] },
  divisionStudents: {},
};

const CISDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [tab, setTab] = useState<(typeof tabs)[number]>("courses");
  const [theoryCourses, setTheoryCourses] = useState<Course[]>([]);
  const [labCourses, setLabCourses] = useState<Course[]>([]);
  const [facultyList, setFacultyList] = useState<
    { name: string; facultyID: string; email: string; mobile?: string }[]
  >([]);
  const [coordinatorList, setCoordinatorList] = useState<{ name: string; facultyID: string }[]>([]);
  const [form, setForm] = useState<FormFields>(initialForm);
  const [errors, setErrors] = useState<Partial<Record<string, string>>>({});
  const [modal, setModal] = useState(false);
  const [selectedTypes, setTypes] = useState({ theory: true, lab: false });
  const [edit, setEdit] = useState({
    is: false,
    index: null as number | null,
    type: null as CourseType | null,
    courseId: null as string | null,
  });
  const [facultyData, setFacultyData] = useState<
    {
      id: string;
      name: string;
      facultyID: string;
      mobile: string;
      email: string;
      courses: string;
      department: string;
    }[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  // Use dynamic departments hook
  const { departments: departmentNames } = useDepartments();
  const departments = ["All Departments", ...departmentNames];

  const [selectedDept, setSelectedDept] = useState("All Departments");
  const [confirmDelete, setConfirmDelete] = useState<{ id: string; type: CourseType } | null>(null);
  const [selectedYearSem, setSelectedYearSem] = useState("");
  const [academicYears, setAcademicYears] = useState<string[]>([]);
  const [yearSemOptions, setYearSemOptions] = useState<string[]>([]);
  const currentUserId = JSON.parse(localStorage.getItem("user") || "{}")._id;
  const [currentUser, setCurrentUser] = useState<{ department: string; progDept: string } | null>(
    null
  );

  // Get the user's department for filtering courses
  const userDepartment = currentUser?.progDept || currentUser?.department;

  const resetForm = () => {
    // Preserve the academic year when resetting the form
    const currentAcademicYear = form.academicyear;
    setForm({ ...initialForm, academicyear: currentAcademicYear });
    setErrors({});
    setTypes({ theory: true, lab: false });
    setEdit({ is: false, index: null, type: null, courseId: null });
  };

  const getUserIds = (ids: string[]): string[] => ids;

  // Function to enroll students in a course
  const enrollStudentsInCourse = async (courseDetailId: string, courseType: "theory" | "lab") => {
    try {
      const allStudentRollNumbers: string[] = [];
      
      // Collect all roll numbers from the imported students
      if (courseType === "theory") {
        for (const div of form.divs) {
          const divisionStudents = form.divisionStudents?.[div] || [];
          divisionStudents.forEach((student) => {
            const [rollNo] = student.includes(",") ? student.split(",") : [student, ""];
            if (rollNo.trim()) {
              allStudentRollNumbers.push(rollNo.trim());
            }
          });
        }
      } else {
        // For lab courses
        const allBatches = selectedTypes.theory && selectedTypes.lab
          ? form.divs.flatMap((div) => form.divisionBatches?.[div] || [])
          : form.batches;
        
        for (const batch of allBatches) {
          let batchStudents: string[] = [];
          
          if (selectedTypes.theory && selectedTypes.lab) {
            // Find which division this batch belongs to
            const divisionForBatch = form.divs.find((div) =>
              form.divisionBatches?.[div]?.includes(batch)
            );
            if (divisionForBatch) {
              batchStudents = form.divisionStudents?.[`${divisionForBatch}_Batch_${batch}`] || [];
            }
          } else {
            batchStudents = form.divisionStudents?.[`Batch_${batch}`] || [];
          }
          
          batchStudents.forEach((student) => {
            const [rollNo] = student.includes(",") ? student.split(",") : [student, ""];
            if (rollNo.trim()) {
              allStudentRollNumbers.push(rollNo.trim());
            }
          });
        }
      }

      if (allStudentRollNumbers.length > 0) {
        console.log(`Enrolling ${allStudentRollNumbers.length} students in ${courseType} course ${courseDetailId}`);
        
        // Call backend API to enroll students
        await axios.post("/api/students/enroll-in-course", {
          courseDetailId,
          courseType,
          rollNumbers: allStudentRollNumbers
        });
        
        console.log(`Successfully enrolled students in ${courseType} course`);
      }
    } catch (error) {
      console.error(`Error enrolling students in ${courseType} course:`, error);
      // Don't throw error to prevent course creation failure
    }
  };

  const getCourses = async (department?: string) => {
    try {
      setLoading(true);
      console.log("Fetching courses for department:", department);
      let apiUrl = "/api/course-offering/list";
      
      // If a specific department is selected and it's not "All Departments", use the department-specific route
      if (department && department !== "All Departments") {
        apiUrl = `/api/course-offering/department/${encodeURIComponent(department)}`;
              // Add academic year filter if specified
      if (form.academicyear) {
        apiUrl += `?academicYear=${encodeURIComponent(form.academicyear)}`;
        console.log("Filtering by academic year:", form.academicyear);
      } else {
        console.log("No academic year filter applied");
      }
      }
      
      console.log("API URL:", apiUrl);
      const res = await axios.get<any[]>(apiUrl);
      console.log("Courses response:", res.data);
      
      const enriched: Course[] = res.data.map((course) => {
        const [year, semester] = course.yearSem.split(" - ");
        const assignedFaculty: Record<string, string[]> = {};

        if (course.type === "theory" && course.divisions) {
          course.divisions.forEach((div: any) => {
            assignedFaculty[`Div ${div.name}`] = div.faculty?.map((f: any) => f.name) || [];
          });
        }
        if (course.type === "lab" && course.batches) {
          course.batches.forEach((batch: any) => {
            assignedFaculty[`Batch ${batch.name}`] = batch.faculty?.map((f: any) => f.name) || [];
          });
        }

        return {
          courseId: course._id,
          coursename: course.courseName,
          coursecode: course.courseCode,
          year,
          semester,
          academicyear: course.academicYear,
          coursecoordinator: course.courseCoordinators?.map((cc: any) => cc.name) || [],
          assignedFaculty,
          type: course.type,
          divs: course.divisions?.map((d: any) => d.name) || [],
          batches: course.batches?.map((b: any) => b.name) || [],
          department: course.department || "",
        };
      });

      setTheoryCourses(enriched.filter((c) => c.type === "theory"));
      setLabCourses(enriched.filter((c) => c.type === "lab"));

      // Compute yearSem options from all courses
      const allCourses = enriched;
      const yearSems = Array.from(
        new Set(allCourses.map((c) => `${c.year} - ${c.semester}`))
      ).sort();
      setYearSemOptions(yearSems);

      // No need to set departments as they come from the hook
      // If current selectedDept is not in the departments list, reset to All Departments
      setSelectedDept((prev) => (departments.includes(prev) ? prev : "All Departments"));
    } catch (err) {
      console.error("Failed to load courses:", err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    setCurrentUser({ department: user.department, progDept: user.progDept });
    axios.get("/api/users/faculty-list").then((res) => setFacultyList(res.data as any));
    axios.get("/api/users/coordinator-list").then((res) => setCoordinatorList(res.data as any));
    
    // Load academic years
    const loadAcademicYears = async () => {
      try {
        console.log("Loading academic years...");
        const academicYearsData = await academicYearService.getAllAcademicYears();
        console.log("Academic years loaded:", academicYearsData);
        setAcademicYears(academicYearsData);
        
        // Set default academic year to current
        const currentYear = await academicYearService.getCurrentAcademicYear();
        console.log("Current academic year:", currentYear);
        console.log("Setting form academicyear to:", currentYear);
        setForm(prev => {
          const newForm = { ...prev, academicyear: currentYear };
          console.log("New form state:", newForm);
          return newForm;
        });
      } catch (error) {
        console.error("Failed to load academic years:", error);
        // Fallback to default values
        setAcademicYears(["2025-2026", "2024-2025", "2023-2024", "2022-2023", "2021-2022"]);
        console.log("Setting fallback academicyear to: 2025-2026");
        setForm(prev => {
          const newForm = { ...prev, academicyear: "2025-2026" };
          console.log("New fallback form state:", newForm);
          return newForm;
        });
      }
    };
    
    loadAcademicYears();
  }, []);

  // Update courses when current user is loaded
  useEffect(() => {
    if (currentUser) {
      const userDepartment = currentUser.progDept || currentUser.department;
      console.log("Current user loaded, department:", userDepartment);
      getCourses(userDepartment);
    }
  }, [currentUser]);

  // Debug form state changes
  useEffect(() => {
    console.log("Form state changed:", form);
  }, [form]);

  // Fetch faculty data when department changes (for faculty tab only)
  useEffect(() => {
    const fetchFacultyData = async () => {
      try {
        let apiUrl;
        if (selectedDept === "All Departments") {
          apiUrl = "/api/users/faculty-with-courses";
        } else {
          // Encode the department name to handle special characters and spaces
          apiUrl = `/api/users/faculty-with-courses/department/${encodeURIComponent(selectedDept)}`;
        }
        
        const res = await axios.get(apiUrl);
        setFacultyData(res.data as any);
      } catch (error) {
        console.error("Error fetching faculty data:", error);
        // Set empty array on error to avoid displaying stale data
        setFacultyData([]);
      }
    };

    // Only fetch if we have departments loaded
    if (departmentNames.length > 0 || selectedDept === "All Departments") {
      fetchFacultyData();
    }
  }, [selectedDept, departmentNames]);

  const submit = async () => {
    console.log("Submit called with form data:", form);
    console.log("Division students:", form.divisionStudents);

    const department = form.yearSemester?.startsWith("FY")
      ? currentUser?.department
      : (currentUser?.progDept ?? currentUser?.department ?? "N/A");

    console.log("Creating course with department:", department);
    console.log("Current user:", currentUser);
    console.log("Year semester:", form.yearSemester);

    const basePayload = {
      courseName: form.coursename,
      courseCode: form.coursecode,
      academicYear: form.academicyear,
      yearSem: form.yearSemester,
      courseCoordinators: getUserIds(form.coursecoordinator),
      department,
    };

    // Build detailsPayload as per courseDetails model
    const detailsPayload = {
      // Reference to course offering will be set after creation
      academicYear: form.academicyear,
      programme: "UG (B.Tech)",
      department,
      type: selectedTypes.theory ? "theory" : "lab",
      courseName: form.coursename,
      courseCode: form.coursecode,
      year: form.yearSemester?.split(" - ")[0] || "",
      semester: form.yearSemester?.split(" - ")[1] || "",
      subjectAbbreviation: "", // Not present in form
      noOfCOs: 0, // Not present in form
      abstract: "", // Not present in form

      modesOfDelivery: [],

      theoryEvaluation: {
        totalTheoryMarks: null,
        ISE: {
          maxMarks: null,
          weightage: null,
          finalMarks: null,
          questions: [],
        },
        ESE: {
          maxMarks: null,
          weightage: null,
          finalMarks: null,
          questions: [],
        },
        IAs: [],
      },

      labEvaluation: {
        totalLabMarks: null,
        totalExperimentMarks: null,
        totalLabCAMarks: null,
        experiments: [],
        labCAComponents: [],
      },

      courseOutcomes: [],
      coToPOMapping: [],
      goalMappings: [],
    };

    const theoryPayload = {
      ...basePayload,
      type: "theory",
      divisions: form.divs.map((div) => ({
        name: div,
        faculty: getUserIds(form.assignedFaculty[`Div ${div}`] || []),
      })),
      batches: [],
    };

    // Fix: When both theory and lab are selected, use divisionBatches, not form.batches
    const allBatches =
      selectedTypes.theory && selectedTypes.lab
        ? form.divs.flatMap((div) => form.divisionBatches?.[div] || [])
        : form.batches;

    const labPayload = {
      ...basePayload,
      type: "lab",
      divisions: [],
      batches: allBatches.map((batch) => ({
        name: batch,
        faculty: getUserIds(form.assignedFaculty[`Batch ${batch}`] || []),
      })),
    };

    // Track created IDs for rollback
    const createdIds: {
      courseOfferingId?: string;
      courseDetailId?: string;
      gradesheetIds?: string[];
    } = {};
    try {
      if (edit.is && edit.index !== null && edit.type) {
        // --- Update existing course ---
        try {
          // Get the existing course to update
          if (!edit.courseId) {
            throw new Error("Could not find existing course to update");
          }

          // Update course offering
          const updatePayload = {
            courseName: form.coursename,
            courseCode: form.coursecode,
            academicYear: form.academicyear,
            yearSem: form.yearSemester,
            courseCoordinators: getUserIds(form.coursecoordinator),
            department,
          };

          if (edit.type === "theory") {
            const theoryUpdatePayload = {
              ...updatePayload,
              type: "theory",
              divisions: form.divs.map((div) => ({
                name: div,
                faculty: getUserIds(form.assignedFaculty[`Div ${div}`] || []),
              })),
              batches: [],
            };

            await axios.put(`/api/course-offering/${edit.courseId}`, theoryUpdatePayload);
          } else if (edit.type === "lab") {
            const labUpdatePayload = {
              ...updatePayload,
              type: "lab",
              divisions: [],
              batches: form.batches.map((batch) => ({
                name: batch,
                faculty: getUserIds(form.assignedFaculty[`Batch ${batch}`] || []),
              })),
            };

            await axios.put(`/api/course-offering/${edit.courseId}`, labUpdatePayload);
          }

          // Update course details if they exist
          try {
            const courseDetailsRes = await axios.get(`/api/courses/by-course-offering/${edit.courseId}`);
            const courseDetails = Array.isArray(courseDetailsRes.data) ? courseDetailsRes.data : [];
            
            if (courseDetails.length > 0) {
              const courseDetail = courseDetails[0];
              await axios.put(`/api/courses/${courseDetail._id}`, {
                ...detailsPayload,
                courseOfferingId: edit.courseId,
              });
            }
          } catch (error) {
            console.warn("Could not update course details:", error);
          }

          // Update gradesheets if they exist
          try {
            const gradesheetsRes = await axios.get(`/api/gradesheets/by-course-offering/${edit.courseId}`);
            const gradesheets = Array.isArray(gradesheetsRes.data) ? gradesheetsRes.data : [];
            
            for (const gradesheet of gradesheets) {
              // Update examiner assignments
              let examinerInternal = "";
              if (gradesheet.type === "theory" && gradesheet.div) {
                examinerInternal = form.assignedFaculty[`Div ${gradesheet.div}`]?.[0] || "";
              } else if (gradesheet.type === "lab" && gradesheet.batch) {
                examinerInternal = form.assignedFaculty[`Batch ${gradesheet.batch}`]?.[0] || "";
              }

              await axios.put(`/api/gradesheets/${gradesheet._id}`, {
                examinerInternal,
                examinerExternal: gradesheet.examinerExternal || "",
              });
            }
          } catch (error) {
            console.warn("Could not update gradesheets:", error);
          }

          alert("Course updated successfully!");
        } catch (error: any) {
          console.error("Error updating course:", error);
          alert("Error updating course: " + (error.response?.data?.error || error.message));
          return;
        }
      } else {
        // --- Create Theory Course, Details, and Gradesheets for each Div ---
        if (selectedTypes.theory) {
          const gradesheetIds: string[] = [];
          const courseRes = await axios.post("/api/course-offering/add", {
            ...theoryPayload,
            createdBy: currentUserId,
          });
          const courseOfferingId = (courseRes.data as any)._id;
          createdIds.courseOfferingId = courseOfferingId;
          // Save courseDetails with reference to courseOfferingId
          const detailsRes = await axios.post<{ _id: string }>("/api/courses/", {
            ...detailsPayload,
            courseOfferingId,
            createdBy: currentUserId,
          });
          const courseDetailId = detailsRes.data._id;
          createdIds.courseDetailId = courseDetailId;
          if (!courseDetailId) throw new Error("Could not determine courseDetailId from response");

          // Enroll students in the theory course
          await enrollStudentsInCourse(courseDetailId, "theory");

          // Create gradesheet for each division
          for (const div of form.divs) {
            // Get student list for this division
            const divisionStudents = form.divisionStudents?.[div] || [];
            console.log(
              `Creating gradesheet for div ${div} with ${divisionStudents.length} students:`,
              divisionStudents
            );

            const studentMarks = divisionStudents.map((student) => {
              // Parse student data (rollNo,name format or just rollNo)
              const [rollNo, name] = student.includes(",") ? student.split(",") : [student, ""];
              return {
                rollNo: rollNo.trim(),
                name: name.trim() || rollNo.trim(),
                marks: [],
                codes: null,
              };
            });

            console.log(`Processed student marks for div ${div}:`, studentMarks);

            // Assign faculty members as examiners
            const examinerInternal = form.assignedFaculty[`Div ${div}`]?.[0] || "";
            
            const gsRes = await axios.post("/api/gradesheets/save", {
              courseOfferingId,
              courseDetailId,
              academicYear: form.academicyear,
              yearSem: form.yearSemester,
              type: "theory",
              div,
              department, // Add department field
              components: [
                { name: "ISE", questions: [], studentMarks: studentMarks, submitted: false },
                { name: "ESE", questions: [], studentMarks: studentMarks, submitted: false },
                { name: "IA-1", questions: [], studentMarks: studentMarks, submitted: false },
                { name: "IA-2", questions: [], studentMarks: studentMarks, submitted: false },
                { name: "IA-3", questions: [], studentMarks: studentMarks, submitted: false },
                { name: "Final CA", questions: [], studentMarks: studentMarks, submitted: false },
              ],
              examinerInternal,
              examinerExternal: "",
              ipAddress: "",
            });
            gradesheetIds.push((gsRes.data as any)._id);
          }
          createdIds.gradesheetIds = gradesheetIds;
        }
        // --- Create Lab Course, Details, and Gradesheets for each Batch ---
        if (selectedTypes.lab) {
          const gradesheetIds: string[] = createdIds.gradesheetIds || [];
          const courseRes = await axios.post("/api/course-offering/add", {
            ...labPayload,
            createdBy: currentUserId,
          });
          const courseOfferingId = (courseRes.data as any)._id;
          createdIds.courseOfferingId = courseOfferingId;
          const detailsRes = await axios.post<{ _id: string }>("/api/courses/", {
            ...detailsPayload,
            type: "lab",
            courseOfferingId,
            createdBy: currentUserId,
          });
          const courseDetailId = detailsRes.data._id;
          createdIds.courseDetailId = courseDetailId;
          if (!courseDetailId) throw new Error("Could not determine courseDetailId from response");

          // Enroll students in the lab course
          await enrollStudentsInCourse(courseDetailId, "lab");

          // Create gradesheet for each batch
          for (const batch of allBatches) {
            // Get student list for this batch
            let batchStudents: string[] = [];

            // Check if we have specific batch students (for theory+lab courses)
            if (selectedTypes.theory && selectedTypes.lab) {
              // Find which division this batch belongs to
              const divisionForBatch = form.divs.find((div) =>
                form.divisionBatches?.[div]?.includes(batch)
              );
              if (divisionForBatch) {
                batchStudents = form.divisionStudents?.[`${divisionForBatch}_Batch_${batch}`] || [];
                console.log(
                  `Creating lab gradesheet for batch ${batch} (from div ${divisionForBatch}) with ${batchStudents.length} students:`,
                  batchStudents
                );
              }
            } else {
              // For lab-only courses, get from batch-specific storage
              batchStudents = form.divisionStudents?.[`Batch_${batch}`] || [];
              console.log(
                `Creating lab gradesheet for batch ${batch} with ${batchStudents.length} students:`,
                batchStudents
              );
            }

            const studentMarks = batchStudents.map((student) => {
              // Parse student data (rollNo,name format or just rollNo)
              const [rollNo, name] = student.includes(",") ? student.split(",") : [student, ""];
              return {
                rollNo: rollNo.trim(),
                name: name.trim() || rollNo.trim(),
                marks: [],
                codes: null,
              };
            });

            console.log(`Processed student marks for batch ${batch}:`, studentMarks);

            // Assign faculty members as examiners
            const examinerInternal = form.assignedFaculty[`Batch ${batch}`]?.[0] || "";
            
            const gsRes = await axios.post("/api/gradesheets/save", {
              courseOfferingId,
              courseDetailId,
              academicYear: form.academicyear,
              yearSem: form.yearSemester,
              type: "lab",
              batch,
              department, // Add department field
              components: [
                {
                  name: "Exp-1",
                  questions: [],
                  studentMarks: studentMarks,
                  submitted: false,
                },
                {
                  name: "Exp-2",
                  questions: [],
                  studentMarks: studentMarks,
                  submitted: false,
                },
                {
                  name: "Lab CA-1",
                  questions: [],
                  studentMarks: studentMarks,
                  submitted: false,
                },
                {
                  name: "Lab CA-2",
                  questions: [],
                  studentMarks: studentMarks,
                  submitted: false,
                },
                {
                  name: "Final Lab CA",
                  questions: [],
                  studentMarks: studentMarks,
                  submitted: false,
                },
              ],
              examinerInternal,
              examinerExternal: "",
              ipAddress: "",
            });
            gradesheetIds.push((gsRes.data as any)._id);
          }
          createdIds.gradesheetIds = gradesheetIds;
        }
        alert("Course(s) and gradesheet(s) added successfully!");
      }

      getCourses(currentUser?.progDept || currentUser?.department);
      resetForm();
      setModal(false);
    } catch (err: any) {
      // Rollback: delete any created items
      if (createdIds.courseOfferingId) {
        try {
          await axios.delete(`/api/course-offering/${createdIds.courseOfferingId}`);
        } catch {}
      }
      if (createdIds.courseDetailId) {
        try {
          await axios.delete(`/api/courses/${createdIds.courseDetailId}`);
        } catch {}
      }
      if (createdIds.gradesheetIds) {
        for (const id of createdIds.gradesheetIds) {
          try {
            await axios.delete(`/api/gradesheets/${id}`);
          } catch {}
        }
      }
      alert("Error: " + (err.response?.data?.error || err.message));
    }
  };

  const editCourse = async (course: Course, i: number) => {
    try {
      const res = await axios.get<FetchedCourse>(`/api/course-offering/${course.courseId}`);
      const fresh = res.data;

      setEdit({ is: true, index: i, type: fresh.type, courseId: course.courseId });
      setTypes({ theory: fresh.type === "theory", lab: fresh.type === "lab" });

      setForm({
        coursename: fresh.courseName,
        coursecode: fresh.courseCode,
        yearSemester: fresh.yearSem,
        coursecoordinator: fresh.courseCoordinators.map((c) => c.facultyID),
        academicyear: fresh.academicYear,
        numDivs: fresh.divisions?.length || 0,
        numBatches: fresh.batches?.length || 0,
        divs: fresh.divisions?.map((d) => d.name) || [],
        batches: fresh.batches?.map((b) => b.name) || [],
        assignedFaculty: {
          ...Object.fromEntries(
            fresh.divisions.map((d) => [`Div ${d.name}`, d.faculty.map((f) => f.facultyID)])
          ),
          ...Object.fromEntries(
            fresh.batches.map((b) => [`Batch ${b.name}`, b.faculty.map((f) => f.facultyID)])
          ),
        },
      });

      setModal(true);
    } catch (err) {
      console.error("Failed to fetch course for editing:", err);
      alert("Could not load course for editing.");
    }
  };

  const removeCourse = async (courseId: string) => {
    try {
      console.log("Deleting course with ID:", courseId);

      // Delete all gradesheets associated with this course offering
      try {
        const gradesheetsRes = await axios.get(`/api/gradesheets/by-course-offering/${courseId}`);
        const gradesheets = Array.isArray(gradesheetsRes.data) ? gradesheetsRes.data : [];

        console.log(`Found ${gradesheets.length} gradesheets to delete`);

        for (const gradesheet of gradesheets) {
          try {
            await axios.delete(`/api/gradesheets/${gradesheet._id}`);
            console.log(`Deleted gradesheet: ${gradesheet._id}`);
          } catch (err) {
            console.warn(`Failed to delete gradesheet ${gradesheet._id}:`, err);
          }
        }
      } catch (err) {
        console.warn("Could not fetch gradesheets for deletion:", err);
        // Try alternative deletion method
        try {
          await axios.delete(`/api/gradesheets/by-course-offering/${courseId}`);
        } catch (altErr) {
          console.warn("Alternative gradesheet deletion failed:", altErr);
        }
      }

      // Delete course details
      try {
        const courseDetailsRes = await axios.get(`/api/courses/by-course-offering/${courseId}`);
        const courseDetails = Array.isArray(courseDetailsRes.data) ? courseDetailsRes.data : [];

        for (const detail of courseDetails) {
          try {
            await axios.delete(`/api/courses/${detail._id}`);
            console.log(`Deleted course detail: ${detail._id}`);
          } catch (err) {
            console.warn(`Failed to delete course detail ${detail._id}:`, err);
          }
        }
      } catch (err) {
        console.warn("Could not fetch course details for deletion:", err);
        // Try alternative deletion method
        try {
          await axios.delete(`/api/courses/by-course-offering/${courseId}`);
        } catch (altErr) {
          console.warn("Alternative course details deletion failed:", altErr);
        }
      }

      // Finally, delete the main course offering
      await axios.delete(`/api/course-offering/${courseId}`);
      console.log(`Deleted course offering: ${courseId}`);

      alert("Course and all related data deleted successfully!");
      getCourses(currentUser?.progDept || currentUser?.department);
    } catch (err: any) {
      console.error("Error deleting course:", err);
      alert("Failed to delete course: " + (err.response?.data?.error || err.message));
    }
  };

  return (
    <div className="flex flex-col h-screen">
      <Navbar role="ciscoordinator" />
      <div className="flex flex-1 overflow-hidden">
        <Sidebar role="ciscoordinator" />
        <main className="flex-1 py-6 px-6 md:px-8 bg-white overflow-auto">
          <h1 className="text-[27px] font-bold text-[#ED1C24] text-center mb-7">Dashboard</h1>
          <div className="mb-4 flex flex-col sm:flex-row items-center justify-end gap-4">
            <div className="flex items-center space-x-3">
              <label className="font-bold text-gray-700">Academic Year:</label>
              <select
                className="border border-gray-300 rounded-md px-3 py-1.5 shadow-sm focus:outline-none focus:ring-2"
                value={form.academicyear}
                onChange={(e) => setForm({ ...form, academicyear: e.target.value })}
              >
                {academicYears.map((year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                ))}
              </select>
            </div>
          </div>
          <div className="flex w-full mb-6 border-b border-gray-200">
            {tabs.map((t) => (
              <button
                key={t}
                onClick={() => setTab(t)}
                className={`w-1/2 text-center py-2 font-semibold text-[17px] ${tab === t ? "text-[#ED1C24] font-bold border-b-4 border-[#ED1C24]" : "text-gray-600 border-b-4 border-transparent hover:text-[#ED1C24]"}`}
              >
                {t[0].toUpperCase() + t.slice(1)}
              </button>
            ))}
          </div>

          {tab === "courses" ? (
            <>
              <div className="mb-6 flex flex-col md:flex-row items-center justify-between gap-4">
                <div className="flex items-center space-x-3">
                  <label className="font-medium text-gray-700">Year - Sem:</label>
                  <select
                    className="border border-gray-300 rounded-md px-3 py-1.5 shadow-sm focus:outline-none focus:ring-2"
                    value={selectedYearSem}
                    onChange={(e) => setSelectedYearSem(e.target.value)}
                  >
                    <option value="">All</option>
                    {yearSemOptions.map((ys) => (
                      <option key={ys} value={ys}>
                        {ys}
                      </option>
                    ))}
                  </select>
                </div>
                {form.academicyear && (
                  <button
                    onClick={() => setModal(true)}
                    className="bg-[#ED1C24] text-white px-6 py-2 rounded-lg font-medium hover:scale-105 transition"
                  >
                    Add Course
                  </button>
                )}
              </div>

              {[
                { label: "Theory Courses", data: theoryCourses },
                { label: "Lab Courses", data: labCourses },
              ].map(({ label, data }) => {
                // Filter courses by selected yearSem and academicYear
                const filteredData = data.filter((course) => {
                  const yearSemMatch = !selectedYearSem || `${course.year} - ${course.semester}` === selectedYearSem;
                  // Only filter by academic year if it's loaded, otherwise show all courses
                  const academicYearMatch = !form.academicyear || course.academicyear === form.academicyear;
                  
                  console.log(`Course ${course.coursecode}: yearSem=${course.year}-${course.semester}, academicYear=${course.academicyear}, form.academicyear=${form.academicyear}`);
                  console.log(`  yearSemMatch: ${yearSemMatch}, academicYearMatch: ${academicYearMatch}`);
                  
                  return yearSemMatch && academicYearMatch;
                });

                return (
                  <section key={label} className="mb-10">
                    <h2 className="text-xl font-semibold text-[#ED1C24] mb-2">{label}</h2>
                    {loading ? (
                      <p className="text-gray-600 italic">Loading {label}...</p>
                    ) : filteredData.length ? (
                      <div
                        className="grid gap-5"
                        style={{ gridTemplateColumns: "repeat(auto-fit, minmax(315px, 1fr))" }}
                      >
                        {filteredData.map((course, i) => (
                          <CISDashboardCard
                            key={`${course.type}-${i}`}
                            {...course}
                            onClick={() =>
                              navigate("/cisdashboard/ciscourse-details", {
                                state: {
                                  code: course.coursecode,
                                  semyear: `${course.year} - ${course.semester}`,
                                  acyear: course.academicyear,
                                  type: course.type,
                                },
                              })
                            }
                            onEdit={() => editCourse(course, i)}
                            onDelete={() =>
                              setConfirmDelete({ id: course.courseId, type: course.type })
                            }
                          />
                        ))}
                      </div>
                    ) : (
                      <p className="text-gray-600 italic">No {label.toLowerCase()} found for the selected filters.</p>
                    )}
                  </section>
                );
              })}

              {modal && (
                <AddCourse
                  form={form}
                  errors={errors}
                  academicYears={academicYears}
                  facultyList={facultyList}
                  selectedTypes={selectedTypes}
                  edit={edit}
                  setForm={setForm}
                  setModal={setModal}
                  setTypes={setTypes}
                  submit={submit}
                  resetForm={resetForm}
                  user={currentUser ?? { department: "", progDept: "" }}
                />
              )}

              {confirmDelete && (
                <div className="fixed inset-0 bg-black bg-opacity-40 z-50 flex justify-center items-center">
                  <div className="bg-white rounded-lg px-6 py-5 w-full max-w-sm shadow-lg">
                    <h2 className="text-lg font-semibold text-center mb-4">
                      Are you sure you want to delete?
                    </h2>
                    <div className="flex justify-center space-x-6">
                      <button
                        className="bg-gray-300 text-black font-medium px-4 py-2 rounded"
                        onClick={() => setConfirmDelete(null)}
                      >
                        Cancel
                      </button>
                      <button
                        className="bg-[#ED1C24] text-white font-medium px-4 py-2 rounded"
                        onClick={() => {
                          if (confirmDelete) {
                            removeCourse(confirmDelete.id);
                            setConfirmDelete(null);
                          }
                        }}
                      >
                        Yes
                      </button>
                    </div>
                  </div>
                </div>
              )}
            </>
          ) : (
            <>
              <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4 mb-5">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 md:gap-4">
                  <label className="text-[16px] font-semibold text-gray-700 min-w-[100px]">
                    Department:
                  </label>
                  <select
                    className="border border-gray-300 rounded-md px-3 py-1.5"
                    value={selectedDept}
                    onChange={(e) => setSelectedDept(e.target.value)}
                  >
                    {departments.map((dept) => (
                      <option key={dept} value={dept}>
                        {dept}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="mt-2 md:mt-0 md:w-[400px]">
                  <input
                    type="text"
                    placeholder="Search by Faculty name or ID"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full border border-gray-300 rounded-2xl px-4 py-1.5"
                  />
                </div>
              </div>

              <div className="overflow-x-auto rounded-lg border">
                <table className="min-w-full table-fixed border border-gray-400">
                  <thead className="bg-gray-100 text-center text-[15px] font-semibold text-gray-700">
                    <tr>
                      <th className="px-4 py-3 border">Sr. No.</th>
                      <th className="px-4 py-3 border">Faculty ID</th>
                      <th className="px-4 py-3 border">Name</th>
                      <th className="px-4 py-3 border">Mobile No.</th>
                      <th className="px-4 py-3 border">Email ID</th>
                    </tr>
                  </thead>
                  <tbody className="bg-white">
                    {facultyData
                      .filter(
                        (f) =>
                          (selectedDept === "All Departments" || f.department === selectedDept) &&
                          (f.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                            f.id.toLowerCase().includes(searchTerm.toLowerCase()))
                      )
                      .map((f, i) => (
                        <tr key={f.id} className="text-[15px] text-gray-800">
                          <td className="px-4 py-2 border text-center">{i + 1}</td>
                          <td className="px-4 py-2 border text-center">{f.facultyID}</td>
                          <td className="px-4 py-2 border">{f.name}</td>
                          <td className="px-4 py-2 border text-center">{f.mobile}</td>
                          <td className="px-4 py-2 border">{f.email}</td>
                        </tr>
                      ))}
                  </tbody>
                </table>
              </div>
            </>
          )}
        </main>
      </div>
    </div>
  );
};

export default CISDashboard;
