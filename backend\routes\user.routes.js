import express from "express";
import User from "../models/User.js";
import CourseOffering from "../models/courseOffering.js";
import { protect } from "../middleware/authMiddleware.js";
import { updateFaculty, createFaculty, deleteFaculty, toggleFacultyStatus } from "../controllers/userController.js";
import { isCIS } from "../middleware/roleMiddleware.js";

const router = express.Router();

router.post("/faculty", createFaculty);
router.put("/faculty/:id", updateFaculty);
router.delete("/faculty/:id", deleteFaculty);
router.patch("/faculty/:id/status", toggleFacultyStatus);
// Faculty list
router.get("/faculty-list", async (req, res) => {
  try {
    const faculty = await User.find({ role: "Faculty" }, "name facultyID");
    res.json(faculty);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

router.get("/Adminfaculty", async (req, res) => {
  try {
    // Include all faculty-type roles, not just "Faculty"
    const facultyRoles = ["Faculty", "Course Coordinator", "CIS Coordinator", "HOD", "Principal", "Exam Cell", "Admin"];
    const faculty = await User.find({ role: { $in: facultyRoles } });
    res.json(faculty);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Coordinator list
router.get("/coordinator-list", async (req, res) => {
  try {
    const coordinators = await User.find({ role: "Course Coordinator" }, "name facultyID");
    res.json(coordinators);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
});

// Faculty with courses
router.get("/faculty-with-courses", async (req, res) => {
  try {
    const users = await User.find({ role: "Faculty" }).select(
      "name facultyID email mobileNumber progDept"
    );
    const offerings = await CourseOffering.find(); // ✅ no populate

    const facultyMap = {};

    offerings.forEach((course) => {
      const { courseName, yearSem, type } = course;
      const courseLabel = `${yearSem} ${courseName}`;

      let facultyList = [];

      if (type === "theory") {
        facultyList = course.divisions?.flatMap((div) => div.faculty || []);
      } else if (type === "lab") {
        facultyList = course.batches?.flatMap((batch) => batch.faculty || []);
      }

      facultyList.forEach((fac) => {
        const facultyID = fac.facultyID;
        if (!facultyID) return;

        if (!facultyMap[facultyID]) facultyMap[facultyID] = new Set();
        facultyMap[facultyID].add(courseLabel);
      });
    });

    const enriched = users.map((fac) => ({
      id: fac._id,
      name: fac.name,
      facultyID: fac.facultyID,
      mobile: fac.mobileNumber,
      email: fac.email,
      department: fac.progDept,
      courses: facultyMap[fac.facultyID] ? Array.from(facultyMap[fac.facultyID]).join(", ") : "",
    }));

    res.json(enriched);
  } catch (err) {
    res.status(500).json({ error: "Internal Server Error" });
  }
});

// Faculty with courses filtered by department
router.get("/faculty-with-courses/department/:department", async (req, res) => {
  try {
    const { department } = req.params;
    
    // Build the query based on department
    let query = { role: "Faculty" };
    if (department && department !== "All Departments") {
      query.progDept = department;
    }

    const users = await User.find(query).select(
      "name facultyID email mobileNumber progDept"
    );
    const offerings = await CourseOffering.find(); // ✅ no populate

    const facultyMap = {};

    offerings.forEach((course) => {
      const { courseName, yearSem, type } = course;
      const courseLabel = `${yearSem} ${courseName}`;

      let facultyList = [];

      if (type === "theory") {
        facultyList = course.divisions?.flatMap((div) => div.faculty || []);
      } else if (type === "lab") {
        facultyList = course.batches?.flatMap((batch) => batch.faculty || []);
      }

      facultyList.forEach((fac) => {
        const facultyID = fac.facultyID;
        if (!facultyID) return;

        if (!facultyMap[facultyID]) facultyMap[facultyID] = new Set();
        facultyMap[facultyID].add(courseLabel);
      });
    });

    const enriched = users.map((fac) => ({
      id: fac._id,
      name: fac.name,
      facultyID: fac.facultyID,
      mobile: fac.mobileNumber,
      email: fac.email,
      department: fac.progDept,
      courses: facultyMap[fac.facultyID] ? Array.from(facultyMap[fac.facultyID]).join(", ") : "",
    }));

    res.json(enriched);
  } catch (err) {
    res.status(500).json({ error: "Internal Server Error" });
  }
});

export default router;
