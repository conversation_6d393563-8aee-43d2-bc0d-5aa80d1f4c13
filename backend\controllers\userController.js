import User from "../models/User.js";
import bcrypt from "bcryptjs";

export const createFaculty = async (req, res) => {
  try {
    const { name, facultyID, email, mobileNumber, department, progDept, username, password, dateOfBirth, role } = req.body;

    // Check if faculty with same facultyID, email, or username already exists
    const existingFaculty = await User.findOne({
      $or: [{ facultyID }, { email }, { username }]
    });

    if (existingFaculty) {
      return res.status(400).json({
        error: existingFaculty.facultyID === facultyID
          ? "Faculty with this ID already exists"
          : existingFaculty.email === email
          ? "Faculty with this email already exists"
          : "Faculty with this username already exists"
      });
    }

    // Hash the password
    const passwordHash = await bcrypt.hash(password, 10);

    // Parse date of birth from DD-MM-YYYY format
    let parsedDateOfBirth = null;
    if (dateOfBirth && dateOfBirth.trim() !== "") {
      const datePattern = /^(\d{1,2})-(\d{1,2})-(\d{4})$/;
      const match = dateOfBirth.match(datePattern);
      if (match) {
        const [, day, month, year] = match;
        // Create date in YYYY-MM-DD format for proper parsing
        parsedDateOfBirth = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
        
        // Validate the parsed date
        if (isNaN(parsedDateOfBirth.getTime())) {
          parsedDateOfBirth = null;
        }
      }
    }

    // Create new faculty
    const faculty = new User({
      name,
      facultyID,
      username,
      email,
      mobileNumber,
      department,
      progDept,
      role: role || "Faculty",
      passwordHash,
      dateOfBirth: parsedDateOfBirth,
      isActive: true
    });

    await faculty.save();

    // Return faculty data without password hash
    const facultyResponse = {
      _id: faculty._id,
      name: faculty.name,
      facultyID: faculty.facultyID,
      username: faculty.username,
      email: faculty.email,
      mobileNumber: faculty.mobileNumber,
      department: faculty.department,
      progDept: faculty.progDept,
      role: faculty.role,
      dateOfBirth: faculty.dateOfBirth,
      isActive: faculty.isActive,
      createdAt: faculty.createdAt,
      updatedAt: faculty.updatedAt
    };

    res.status(201).json(facultyResponse);
  } catch (err) {
    console.error("Error creating faculty:", err);
    res.status(500).json({ error: err.message });
  }
};

export const updateFaculty = async (req, res) => {
  const { id } = req.params;
  const { name, username, email, mobileNumber, department, progDept, role, password, dateOfBirth, isActive } = req.body;

  try {
    const faculty = await User.findById(id);
    if (!faculty) return res.status(404).json({ error: "Faculty not found" });

    // Check for duplicate username if username is being updated
    if (username !== undefined && username !== faculty.username) {
      const existingUser = await User.findOne({ username });
      if (existingUser && existingUser._id.toString() !== id) {
        return res.status(400).json({ error: "Username already exists" });
      }
    }

    // Check for duplicate email if email is being updated
    if (email !== undefined && email !== faculty.email) {
      const existingUser = await User.findOne({ email });
      if (existingUser && existingUser._id.toString() !== id) {
        return res.status(400).json({ error: "Email already exists" });
      }
    }

    // Update fields if provided
    if (name !== undefined) faculty.name = name;
    if (username !== undefined) faculty.username = username;
    if (email !== undefined) faculty.email = email;
    if (mobileNumber !== undefined) faculty.mobileNumber = mobileNumber;
    if (department !== undefined) faculty.department = department;
    if (progDept !== undefined) faculty.progDept = progDept;
    if (role !== undefined) faculty.role = role;
    if (isActive !== undefined) faculty.isActive = isActive;

    // Parse date of birth from DD-MM-YYYY format if provided
    if (dateOfBirth !== undefined) {
      if (dateOfBirth && dateOfBirth.trim() !== "") {
        const datePattern = /^(\d{1,2})-(\d{1,2})-(\d{4})$/;
        const match = dateOfBirth.match(datePattern);
        if (match) {
          const [, day, month, year] = match;
          // Create date in YYYY-MM-DD format for proper parsing
          const parsedDate = new Date(`${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`);
          
          // Validate the parsed date
          if (!isNaN(parsedDate.getTime())) {
            faculty.dateOfBirth = parsedDate;
          }
        }
      } else {
        faculty.dateOfBirth = null;
      }
    }

    // Update password if provided
    if (password) {
      faculty.passwordHash = await bcrypt.hash(password, 10);
    }

    await faculty.save();

    // Return updated faculty data without password hash
    const facultyResponse = {
      _id: faculty._id,
      name: faculty.name,
      facultyID: faculty.facultyID,
      username: faculty.username,
      email: faculty.email,
      mobileNumber: faculty.mobileNumber,
      department: faculty.department,
      progDept: faculty.progDept,
      role: faculty.role,
      dateOfBirth: faculty.dateOfBirth,
      isActive: faculty.isActive,
      updatedAt: faculty.updatedAt
    };

    res.json(facultyResponse);
  } catch (err) {
    console.error("Error updating faculty:", err);
    res.status(500).json({ error: err.message });
  }
};

export const deleteFaculty = async (req, res) => {
  const { id } = req.params;

  try {
    console.log("Attempting to delete faculty with ID:", id);

    // Validate ObjectId format
    if (!id || !id.match(/^[0-9a-fA-F]{24}$/)) {
      console.log("Invalid ObjectId format:", id);
      return res.status(400).json({
        success: false,
        error: "Invalid faculty ID format"
      });
    }

    let faculty = await User.findById(id);
    console.log("Faculty found by MongoDB ID:", faculty ? `${faculty.name} (${faculty.role})` : "null");

    // If not found by MongoDB ID, try to find by facultyID as fallback
    if (!faculty) {
      console.log("Trying to find faculty by facultyID:", id);
      faculty = await User.findOne({ facultyID: id });
      console.log("Faculty found by facultyID:", faculty ? `${faculty.name} (${faculty.role})` : "null");
    }

    if (!faculty) {
      console.log("Faculty not found in database by either MongoDB ID or facultyID");
      return res.status(404).json({
        success: false,
        error: "Faculty not found"
      });
    }

    // Check if faculty is a faculty role
    const facultyRoles = ["Faculty", "Course Coordinator", "CIS Coordinator", "HOD", "Principal", "Exam Cell", "Admin"];
    if (!facultyRoles.includes(faculty.role)) {
      return res.status(400).json({
        success: false,
        error: "User is not a faculty member"
      });
    }

    // Use the faculty's actual MongoDB _id for deletion
    await User.findByIdAndDelete(faculty._id);
    console.log("Faculty deleted successfully:", faculty.name);

    res.json({
      success: true,
      message: `${faculty.role} ${faculty.name} deleted successfully`
    });
  } catch (err) {
    console.error("Error deleting faculty:", err);
    res.status(500).json({
      success: false,
      error: "Failed to delete faculty: " + err.message
    });
  }
};

export const toggleFacultyStatus = async (req, res) => {
  const { id } = req.params;
  const { isActive } = req.body;

  try {
    const faculty = await User.findById(id);
    if (!faculty) return res.status(404).json({ error: "Faculty not found" });

    faculty.isActive = isActive;
    await faculty.save();

    // Return updated faculty data without password hash
    const facultyResponse = {
      _id: faculty._id,
      name: faculty.name,
      facultyID: faculty.facultyID,
      email: faculty.email,
      mobileNumber: faculty.mobileNumber,
      department: faculty.department,
      progDept: faculty.progDept,
      role: faculty.role,
      dateOfBirth: faculty.dateOfBirth,
      isActive: faculty.isActive,
      updatedAt: faculty.updatedAt
    };

    res.json(facultyResponse);
  } catch (err) {
    console.error("Error toggling faculty status:", err);
    res.status(500).json({ error: err.message });
  }
};
