import nodemailer from 'nodemailer';
import dotenv from 'dotenv';

dotenv.config();

class EmailService {
  constructor() {
    this.transporter = nodemailer.createTransporter({
      service: 'gmail', // You can change this to other email services
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASSWORD // Use App Password for Gmail
      }
    });
  }

  async sendFacultyCredentials(facultyData, plainPassword) {
    try {
      const { name, email, username, facultyID, role, department } = facultyData;
      
      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: email,
        subject: 'Welcome to CIS Portal - Your Account Credentials',
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background-color: #B7202E; color: white; padding: 20px; text-align: center; }
              .content { padding: 20px; background-color: #f9f9f9; }
              .credentials { background-color: white; padding: 15px; border-left: 4px solid #B7202E; margin: 20px 0; }
              .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
              .button { display: inline-block; padding: 10px 20px; background-color: #B7202E; color: white; text-decoration: none; border-radius: 5px; margin: 10px 0; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1>Welcome to CIS Portal</h1>
                <p>K. J. Somaiya Institute of Technology</p>
              </div>
              
              <div class="content">
                <h2>Dear ${name},</h2>
                
                <p>Welcome to the CIS Portal! Your account has been successfully created with the following details:</p>
                
                <div class="credentials">
                  <h3>Your Login Credentials:</h3>
                  <p><strong>Faculty ID:</strong> ${facultyID}</p>
                  <p><strong>Username:</strong> ${username}</p>
                  <p><strong>Email:</strong> ${email}</p>
                  <p><strong>Password:</strong> ${plainPassword}</p>
                  <p><strong>Role:</strong> ${role}</p>
                  <p><strong>Department:</strong> ${department}</p>
                </div>
                
                <p><strong>Important Security Notes:</strong></p>
                <ul>
                  <li>Please change your password after your first login</li>
                  <li>Keep your credentials secure and do not share them with anyone</li>
                  <li>You can login using either your username or email address</li>
                </ul>
                
                <p>You can access the CIS Portal at: <a href="${process.env.FRONTEND_URL || 'http://localhost:5173'}" class="button">Login to CIS Portal</a></p>
                
                <p>If you have any questions or need assistance, please contact the system administrator.</p>
                
                <p>Best regards,<br>
                CIS Portal Administration Team<br>
                K. J. Somaiya Institute of Technology</p>
              </div>
              
              <div class="footer">
                <p>This is an automated email. Please do not reply to this message.</p>
                <p>&copy; ${new Date().getFullYear()} K. J. Somaiya Institute of Technology. All rights reserved.</p>
              </div>
            </div>
          </body>
          </html>
        `
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Email sent successfully:', result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending email:', error);
      return { success: false, error: error.message };
    }
  }

  async sendPasswordReset(email, name, newPassword) {
    try {
      const mailOptions = {
        from: process.env.EMAIL_USER,
        to: email,
        subject: 'CIS Portal - Password Reset',
        html: `
          <!DOCTYPE html>
          <html>
          <head>
            <style>
              body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
              .container { max-width: 600px; margin: 0 auto; padding: 20px; }
              .header { background-color: #B7202E; color: white; padding: 20px; text-align: center; }
              .content { padding: 20px; background-color: #f9f9f9; }
              .credentials { background-color: white; padding: 15px; border-left: 4px solid #B7202E; margin: 20px 0; }
              .footer { text-align: center; padding: 20px; color: #666; font-size: 12px; }
            </style>
          </head>
          <body>
            <div class="container">
              <div class="header">
                <h1>Password Reset - CIS Portal</h1>
              </div>
              
              <div class="content">
                <h2>Dear ${name},</h2>
                
                <p>Your password has been reset by the system administrator.</p>
                
                <div class="credentials">
                  <h3>Your New Password:</h3>
                  <p><strong>Password:</strong> ${newPassword}</p>
                </div>
                
                <p><strong>Important:</strong> Please change this password after logging in for security purposes.</p>
                
                <p>Best regards,<br>
                CIS Portal Administration Team</p>
              </div>
              
              <div class="footer">
                <p>This is an automated email. Please do not reply to this message.</p>
              </div>
            </div>
          </body>
          </html>
        `
      };

      const result = await this.transporter.sendMail(mailOptions);
      console.log('Password reset email sent successfully:', result.messageId);
      return { success: true, messageId: result.messageId };
    } catch (error) {
      console.error('Error sending password reset email:', error);
      return { success: false, error: error.message };
    }
  }
}

export default new EmailService();
