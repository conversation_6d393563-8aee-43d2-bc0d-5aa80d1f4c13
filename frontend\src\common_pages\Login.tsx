import React, { useState, useEffect } from "react";
import axios from "axios";
import { useNavigate, useLocation } from "react-router-dom";

import bgImage from "../assets/bg.png";
import logo from "../assets/KJSCE-Logo.jpeg";
import logo2 from "../assets/logo2.jpeg";
import { MdEmail } from "react-icons/md";
import { useAuth, Role } from "../contexts/AuthContext";

// Define the expected response type
interface LoginResponse {
  token: string;
  user: {
    id: string;
    name: string;
    email: string;
    role: string;
    department?: string;
    progDept?: string;
  };
}

// Role mapping from backend to frontend
const roleMapping: Record<string, Role> = {
  Faculty: "faculty",
  "CIS Coordinator": "ciscoordinator",
  "Course Coordinator": "coursecoordinator",
  HOD: "hod",
  EIC: "examcell",
  Principal: "principal",
  Admin: "admin",
};

const LoginPage: React.FC = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const { login, user, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  const from = location.state?.from?.pathname || "/";

  // Redirect if already logged in
  useEffect(() => {
    if (user && !isLoading) {
      const roleRoutes: Record<Role, string> = {
        faculty: "/fdashboard",
        ciscoordinator: "/cisdashboard",
        coursecoordinator: "/ccdashboard",
        hod: "/hoddashboard",
        examcell: "/eicdashboard",
        principal: "/prdashboard",
        admin: "/addashboard",
      };
      const targetRoute = roleRoutes[user.role];
      if (targetRoute && location.pathname !== targetRoute) {
        navigate(targetRoute, { replace: true });
      }
    }
  }, [user, navigate, location.pathname, isLoading]);

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoggingIn(true);

    try {
      const res = await axios.post<LoginResponse>(`${import.meta.env.VITE_API_BASE_URL || "http://localhost:7000"}/api/auth/login`, {
        email,
        password,
      });
      console.log(res.data);
      // Map backend role to frontend role
      const frontendRole = roleMapping[res.data.user.role];

      const userData = {
        ...res.data.user,
        role: frontendRole,
      };

      login(res.data.token, userData);
    } catch (err: any) {
      alert(err.response?.data?.error || "Login failed");
    } finally {
      setIsLoggingIn(false);
    }
  };

  // Don't render login form if user is already authenticated
  if (user && !isLoading) {
    return null; // or a loading spinner
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-100">
      {/* Header */}
      <header className="relative flex justify-between items-center px-8 py-4 bg-white shadow-md">
        <img src={logo} alt="Somaiya Logo" className="w-[250px]" />
        <h1 className="absolute left-1/2 transform -translate-x-1/2 md:text-[27px] mt-3 font-semibold text-gray-800">
          Welcome to CIS Portal
        </h1>
        <img src={logo2} alt="Somaiya Trust Logo" className="h-12" />
      </header>

      {/* Background with Login Box */}
      <div
        className="flex items-center justify-end flex-1 bg-cover bg-center pr-20"
        style={{ backgroundImage: `url(${bgImage})` }}
      >
        <div className="bg-white bg-opacity-75 backdrop-blur-md rounded-xl p-8 shadow-lg w-96">
          <h2 className="text-[25px] font-bold mb-8 text-center">CIS Portal</h2>

          <form onSubmit={handleLogin}>
            <div className="mb-4">
              <label htmlFor="email" className="block mb-1 text-sm font-medium text-gray-700">
                Username or Email:
              </label>
              <input
                id="email"
                type="text"
                required
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-400"
                placeholder="Enter username or email"
              />
            </div>

            <div className="mb-4">
              <label htmlFor="password" className="block mb-1 text-sm font-medium text-gray-700">
                Password:
              </label>
              <input
                id="password"
                type="password"
                required
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="w-full border border-gray-300 rounded px-3 py-2 focus:ring-2 focus:ring-blue-400"
              />
            </div>

            <div className="flex items-center justify-between text-sm mb-4">
              <label className="flex items-center text-gray-700">
                <input type="checkbox" className="mr-1.5 mt-1" />
                Remember me
              </label>
              <a href="#" className="text-blue-600 hover:underline">
                Forgot Password?
              </a>
            </div>

            <button
              type="submit"
              disabled={isLoggingIn}
              className="w-full bg-[#B7202E] text-white py-2 rounded-lg hover:bg-red-800 transition disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isLoggingIn ? "Logging in..." : "Log in"}
            </button>
          </form>

          <div className="text-center text-gray-500 my-4">OR</div>

          <button
            type="button"
            className="w-full bg-gray-200 py-2 rounded flex items-center justify-center gap-2 hover:bg-gray-300 transition"
            onClick={() => alert("Coming soon")}
          >
            <MdEmail className="text-lg" />
            Login with Somaiya Email ID
          </button>
        </div>
      </div>
    </div>
  );
};

export default LoginPage;
