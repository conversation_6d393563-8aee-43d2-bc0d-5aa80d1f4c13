import React, { useState, useEffect } from "react";
import { EyeIcon, EyeSlashIcon } from "@heroicons/react/24/outline";
import facultyService from "../../services/facultyService";

interface Faculty {
  id: string;
  _id?: string; // MongoDB ID for backend operations
  name: string;
  dob: string;
  email: string;
  mobile: string;
  department: string;
  role?: string;
  username: string;
  password: string;
}

interface Props {
  faculty: Faculty;
  onClose: () => void;
  onSave: (faculty: Faculty) => void;
  onDisable: (id: string) => void;
  onDelete: (id: string) => void;
  isEditMode: boolean;
  departments: string[];
}

const EditAddFacultyModal: React.FC<Props> = ({
  faculty,
  onClose,
  onSave,
  onDisable,
  onDelete,
  isEditMode,
  departments,
}) => {
  const [formData, setFormData] = useState<Faculty>(faculty);

  // Helper function to parse date and extract day, month, year
  const parseDateString = (dateStr: string) => {
    if (!dateStr) return ["", "", ""];
    
    // Handle DD-MM-YYYY format
    if (dateStr.includes("-") && dateStr.split("-").length === 3) {
      const parts = dateStr.split("-");
      return [parts[0], parts[1], parts[2]];
    }
    
    // Handle DD/MM/YYYY format (from Date.toLocaleDateString)
    if (dateStr.includes("/") && dateStr.split("/").length === 3) {
      const parts = dateStr.split("/");
      return [parts[0], parts[1], parts[2]];
    }
    
    // Handle other date formats by trying to parse as Date
    try {
      const date = new Date(dateStr);
      if (!isNaN(date.getTime())) {
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear().toString();
        return [day, month, year];
      }
    } catch (error) {
      console.warn("Could not parse date:", dateStr);
    }
    
    return ["", "", ""];
  };

  const [dobDay, setDobDay] = useState("");
  const [dobMonth, setDobMonth] = useState("");
  const [dobYear, setDobYear] = useState("");
  const [dobTouched, setDobTouched] = useState(false);

  const [isDobValid, setIsDobValid] = useState(true);
  const [isEmailValid, setIsEmailValid] = useState(faculty.email.endsWith("@somaiya.edu"));
  const [isMobileValid, setIsMobileValid] = useState(/^\d{10}$/.test(faculty.mobile));

  const [isResetPassword, setIsResetPassword] = useState(false);
  const [newPassword, setNewPassword] = useState("");
  const [confirmNewPassword, setConfirmNewPassword] = useState("");
  const [isNewPasswordValid, setIsNewPasswordValid] = useState(false);
  const [isPasswordMatch, setIsPasswordMatch] = useState(true);
  const [showPassword, setShowPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);

  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [showDisableConfirm, setShowDisableConfirm] = useState(false);
  const [showRemoveConfirm, setShowRemoveConfirm] = useState(false);

  // Update form data when faculty prop changes
  useEffect(() => {
    setFormData(faculty);
    
    // Parse and set DOB fields
    const [day, month, year] = parseDateString(faculty.dob);
    setDobDay(day);
    setDobMonth(month);
    setDobYear(year);
    setDobTouched(false);
  }, [faculty]);

  const generateSecurePassword = () => {
    const upper = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const lower = "abcdefghijklmnopqrstuvwxyz";
    const digits = "0123456789";
    const special = "!@#$%^&*()_+[]{}|;:,.<>?";
    const all = upper + lower + digits + special;

    const getRandom = (str: string) => str[Math.floor(Math.random() * str.length)];

    let password = [getRandom(upper), getRandom(digits), getRandom(special)];

    for (let i = password.length; i < 8; i++) {
      password.push(getRandom(all));
    }

    return password.sort(() => 0.5 - Math.random()).join("");
  };

  const getPasswordErrors = (password: string) => ({
    length: password.length >= 8,
    capital: /[A-Z]/.test(password),
    number: /[0-9]/.test(password),
    special: /[!@#$%^&*(),.?":{}|<>]/.test(password),
  });

  const [passwordErrors, setPasswordErrors] = useState({
    length: false,
    capital: false,
    number: false,
    special: false,
  });

  useEffect(() => {
    // Allow either all fields empty or all fields valid
    const hasAllFields = dobDay !== "" && dobMonth !== "" && dobYear !== "";
    const hasNoFields = dobDay === "" && dobMonth === "" && dobYear === "";
    
    if (hasNoFields) {
      setIsDobValid(true);
    } else if (hasAllFields) {
      const validDay = /^\d{1,2}$/.test(dobDay) && Number(dobDay) >= 1 && Number(dobDay) <= 31;
      const validMonth = /^\d{1,2}$/.test(dobMonth) && Number(dobMonth) >= 1 && Number(dobMonth) <= 12;
      const validYear = /^\d{4}$/.test(dobYear) && Number(dobYear) >= 1900 && Number(dobYear) <= new Date().getFullYear();
      setIsDobValid(validDay && validMonth && validYear);
    } else {
      // Partial date entry - invalid
      setIsDobValid(false);
    }
  }, [dobDay, dobMonth, dobYear]);

  useEffect(() => {
    setIsEmailValid(formData.email.endsWith("@somaiya.edu"));
    setIsMobileValid(/^\d{10}$/.test(formData.mobile));
  }, [formData]);

  useEffect(() => {
    if (!isEditMode) {
      const autoPassword = generateSecurePassword();
      setFormData((prev) => ({ ...prev, password: autoPassword }));
    }
  }, [isEditMode]);

  const handleDisable = async () => {
    try {
      // Use _id for backend operations, fallback to id if _id not available
      const backendId = formData._id || formData.id;
      await facultyService.toggleFacultyStatus(backendId, false);

      onDisable(formData.id);
      setShowDisableConfirm(false);
      onClose();
    } catch (error) {
      console.error("Error disabling faculty:", error);
      alert("Error disabling faculty: " + (error as Error).message);
    }
  };

  const handleDelete = async () => {
    try {
      // Use _id for backend operations, fallback to id if _id not available
      const backendId = formData._id || formData.id;
      const result = await facultyService.deleteFaculty(backendId);

      if (result.success) {
        alert(result.message || "Faculty deleted successfully");
        onDelete(formData.id);
        setShowRemoveConfirm(false);
        onClose();
      } else {
        alert("Error: " + (result.message || "Failed to delete faculty"));
      }
    } catch (error: any) {
      console.error("Error deleting faculty:", error);
      const errorMessage = error.response?.data?.error || error.message || "Failed to delete faculty";
      alert("Error deleting faculty: " + errorMessage);
    }
  };

  const allValid =
    isEmailValid &&
    isMobileValid &&
    isDobValid &&
    formData.name.trim() !== "" &&
    formData.username.trim() !== "" &&
    (!isEditMode || !isResetPassword || (isNewPasswordValid && isPasswordMatch));

  const handleSave = () => {
    // Check if date fields are either all provided or all empty
    const hasAllDateFields = dobDay !== "" && dobMonth !== "" && dobYear !== "";
    const hasNoDateFields = dobDay === "" && dobMonth === "" && dobYear === "";
    const dobCheck = hasAllDateFields || hasNoDateFields;
    
    setIsDobValid(dobCheck);
    if (!dobCheck) return;

    let formattedDob = "";
    if (hasAllDateFields) {
      const paddedDay = dobDay.padStart(2, "0");
      const paddedMonth = dobMonth.padStart(2, "0");
      formattedDob = `${paddedDay}-${paddedMonth}-${dobYear}`;
    }

    const updatedFaculty = {
      ...formData,
      dob: formattedDob,
      password: isResetPassword ? newPassword : formData.password,
    };

    onSave(updatedFaculty);
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-40 flex items-center justify-center z-50">
      <div className="bg-white px-6 py-5 rounded-lg w-[570px] space-y-8 shadow-lg relative">
        {/* Top Header */}
        <div className="relative w-full">
          <h2 className="text-[#ED1C24] text-2xl font-bold text-center">
            {isEditMode ? `${formData.id} - ${formData.name}` : "Add Faculty"}
          </h2>
          {isEditMode && (
            <button
              onClick={() => setShowRemoveConfirm(true)}
              className="absolute right-0 top-2 text-red-500 hover:underline text-[14px]"
            >
              Delete
            </button>
          )}
        </div>

        <div className="space-y-3">
          {/* Department */}
          <div className="flex items-center">
            <label className="w-[110px] font-medium">Department:</label>
            {isEditMode ? (
              <span className="text-[16px]">{formData.department}</span>
            ) : (
              <select
                className="ml-3.5 border px-3 py-1.5 rounded w-full"
                value={formData.department}
                onChange={(e) => setFormData({ ...formData, department: e.target.value })}
              >
                {departments.map((d) => (
                  <option key={d}>{d}</option>
                ))}
              </select>
            )}
          </div>

          {/* Role */}
          <div className="flex items-center">
            <label className="w-[110px] font-medium">Role:</label>
            {isEditMode ? (
              <span className="text-[16px]">{formData.role || "Faculty"}</span>
            ) : (
              <select
                className="ml-3.5 border px-3 py-1.5 rounded w-full"
                value={formData.role || "Faculty"}
                onChange={(e) => setFormData({ ...formData, role: e.target.value })}
              >
                <option value="Faculty">Faculty</option>
                <option value="Course Coordinator">Course Coordinator</option>
                <option value="CIS Coordinator">CIS Coordinator</option>
                <option value="HOD">HOD</option>
                <option value="Principal">Principal</option>
                <option value="Exam Cell">Exam Cell</option>
                <option value="Admin">Admin</option>
              </select>
            )}
          </div>

          {!isEditMode && (
            <div className="flex items-center text-[16px] font-medium">
              <label className="w-[105px]">Faculty ID:</label>
              <span className="text-[17px]">{formData.id}</span>
            </div>
          )}

          <div className="flex items-center">
            <label className="w-[135px] font-medium">Name:</label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="border px-3 py-1.5 rounded w-full"
            />
          </div>

          <div className="flex items-center mt-3">
            <label className="w-[135px] font-medium">Date of Birth:</label>
            <div className="flex flex-col w-full">
              <div className="flex items-center gap-1">
                {/* Day */}
                <input
                  type="text"
                  placeholder="DD"
                  value={dobDay}
                  onChange={(e) => {
                    setDobTouched(true);
                    const val = e.target.value.replace(/\D/g, "").slice(0, 2);
                    if (val === "" || (parseInt(val) >= 1 && parseInt(val) <= 31)) {
                      setDobDay(val);
                    }
                  }}
                  className="border px-2 py-1.5 rounded w-[60px] text-center"
                />
                <span className="mx-1 font-bold">-</span>

                {/* Month */}
                <input
                  type="text"
                  placeholder="MM"
                  value={dobMonth}
                  onChange={(e) => {
                    setDobTouched(true);
                    const val = e.target.value.replace(/\D/g, "").slice(0, 2);
                    if (val === "" || (parseInt(val) >= 1 && parseInt(val) <= 12)) {
                      setDobMonth(val);
                    }
                  }}
                  className="border px-2 py-1.5 rounded w-[60px] text-center"
                />
                <span className="mx-1 font-bold">-</span>

                {/* Year */}
                <input
                  type="text"
                  placeholder="YYYY"
                  value={dobYear}
                  onChange={(e) => {
                    setDobTouched(true);
                    const val = e.target.value.replace(/\D/g, "").slice(0, 4);
                    const num = parseInt(val);
                    const currentYear = new Date().getFullYear();
                    if (val === "" || val.length < 4 || (num >= 1900 && num <= currentYear)) {
                      setDobYear(val);
                    }
                  }}
                  className="border px-2 py-1.5 rounded w-[75px] text-center"
                />
              </div>
              {!isDobValid && dobTouched && (
                <p className="text-red-500 text-sm mt-1 ml-1">Invalid</p>
              )}
            </div>
          </div>

          <div className="flex flex-col">
            <div className="flex items-center">
              <label className="w-[135px] font-medium">Mobile No.:</label>
              <div className="flex w-full">
                <span className="px-3 py-1.5 border border-r-0 bg-gray-100 rounded-l">+91</span>
                <input
                  type="text"
                  value={formData.mobile}
                  onChange={(e) => {
                    const input = e.target.value.replace(/\D/g, "");
                    setFormData({ ...formData, mobile: input });
                    setIsMobileValid(/^\d{10}$/.test(input));
                  }}
                  placeholder="10-digit number"
                  className="border px-3 py-1.5 rounded-r w-full"
                  maxLength={10}
                  inputMode="numeric"
                />
              </div>
            </div>
            {!isMobileValid && formData.mobile !== "" && (
              <p className="text-red-500 text-sm ml-[105px] mt-1">Must be 10 digits</p>
            )}
          </div>

          <div className="flex flex-col">
            <div className="flex items-center">
              <label className="w-[135px] font-medium">Email ID:</label>
              <input
                type="email"
                value={formData.email}
                onChange={(e) => {
                  const value = e.target.value;
                  setFormData({ ...formData, email: value });
                  setIsEmailValid(value.endsWith("@somaiya.edu"));
                }}
                className="border px-3 py-1.5 rounded w-full"
              />
            </div>
            {!isEmailValid && formData.email !== "" && (
              <p className="text-red-500 text-sm ml-[105px] mt-1">Invalid Email</p>
            )}
          </div>

          <div className="flex items-center">
            <label className="w-[135px] font-medium">Username:</label>
            <input
              type="text"
              value={formData.username}
              onChange={(e) => setFormData({ ...formData, username: e.target.value })}
              className="border px-3 py-1.5 rounded w-full"
            />
          </div>

          {/* Password Section */}
          {isEditMode && !isResetPassword ? (
            <>
              <div className="flex items-center">
                <label className="w-[135px] font-medium">Password:</label>
                <div className="relative w-full">
                  <input
                    type={showPassword ? "text" : "password"}
                    className="border px-3 py-1.5 rounded w-full pr-10 bg-gray-100"
                    value={formData.password || ""}
                    disabled
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-2 top-1.5"
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-500" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-500" />
                    )}
                  </button>
                </div>
              </div>
              <div className="flex justify-end mt-1">
                <button
                  onClick={() => setIsResetPassword(true)}
                  className="text-sm text-blue-600 ml-[135px] hover:underline"
                >
                  Reset Password
                </button>
              </div>
            </>
          ) : isEditMode && isResetPassword ? (
            <>
              {/* New Password */}
              <div className="flex flex-col">
                <div className="flex items-center">
                  <label className="w-[200px] font-medium">New Password:</label>
                  <div className="relative w-full">
                    <input
                      type={showNewPassword ? "text" : "password"}
                      className="border px-3 py-1.5 rounded w-full pr-10"
                      value={newPassword}
                      onChange={(e) => {
                        const val = e.target.value;
                        setNewPassword(val);
                        const errors = getPasswordErrors(val);
                        setPasswordErrors(errors);
                        setIsNewPasswordValid(Object.values(errors).every(Boolean));
                        setIsPasswordMatch(val === confirmNewPassword);
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowNewPassword(!showNewPassword)}
                      className="absolute right-2 top-1.5"
                    >
                      {showNewPassword ? (
                        <EyeSlashIcon className="h-5 w-5 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </button>
                  </div>
                </div>
                {newPassword !== "" && (
                  <ul className="text-red-500 text-sm ml-[1px] mt-1 space-y-1 list-disc list-inside">
                    {!passwordErrors.length && <li>At least 8 characters</li>}
                    {!passwordErrors.capital && <li>One uppercase letter</li>}
                    {!passwordErrors.number && <li>One number</li>}
                    {!passwordErrors.special && <li>One special character</li>}
                  </ul>
                )}
              </div>

              {/* Confirm Password */}
              <div className="flex flex-col">
                <div className="flex items-center">
                  <label className="w-[200px] font-medium">Confirm Password:</label>
                  <div className="relative w-full">
                    <input
                      type={showConfirmPassword ? "text" : "password"}
                      className="border px-3 py-1.5 rounded w-full pr-10"
                      value={confirmNewPassword}
                      onChange={(e) => {
                        const val = e.target.value;
                        setConfirmNewPassword(val);
                        setIsPasswordMatch(val === newPassword);
                      }}
                    />
                    <button
                      type="button"
                      onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                      className="absolute right-2 top-1.5"
                    >
                      {showConfirmPassword ? (
                        <EyeSlashIcon className="h-5 w-5 text-gray-500" />
                      ) : (
                        <EyeIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </button>
                  </div>
                </div>
                {!isPasswordMatch && confirmNewPassword !== "" && (
                  <p className="text-red-500 text-sm ml-[140px] mt-1">Does not match</p>
                )}
              </div>
            </>
          ) : (
            <div className="flex items-center">
              <label className="w-[135px] font-medium">Password:</label>
              <div className="relative w-full">
                <input
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  disabled
                  className="border px-3 py-1.5 rounded w-full pr-10 bg-gray-100"
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-2 top-1.5"
                >
                  {showPassword ? (
                    <EyeSlashIcon className="h-5 w-5 text-gray-500" />
                  ) : (
                    <EyeIcon className="h-5 w-5 text-gray-500" />
                  )}
                </button>
              </div>
            </div>
          )}
        </div>

        <div className="flex justify-between font-medium mt-5">
          {isEditMode && (
            <div className="flex gap-2">
              <button
                onClick={() => setShowDisableConfirm(true)}
                className="px-4 py-2 bg-yellow-400 text-black rounded hover:bg-yellow-500"
              >
                Disable
              </button>
            </div>
          )}
          <div className="flex gap-2 ml-auto">
            <button onClick={onClose} className="px-4 py-2 bg-gray-200 rounded hover:bg-gray-300">
              Cancel
            </button>
            <button
              onClick={handleSave}
              className={`px-4 py-2 text-white rounded ${
                allValid ? "bg-[#39B031] hover:bg-[#379730]" : "bg-gray-400 cursor-not-allowed"
              }`}
              disabled={!allValid}
            >
              {isEditMode ? "Save" : "Add"}
            </button>
          </div>
        </div>

        {/* Disable Confirm */}
        {showDisableConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg text-center">
              <p className="mb-4 text-lg">
                Are you sure you want to <strong>disable</strong>{" "}
                <span className="text-[#ED1C24]">{formData.name}</span>?
              </p>
              <div className="flex justify-center gap-4">
                <button
                  onClick={() => setShowDisableConfirm(false)}
                  className="px-4 py-2 bg-gray-300 rounded hover:bg-gray-400"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDisable}
                  className="px-4 py-2 bg-yellow-500 text-black rounded hover:bg-yellow-600"
                >
                  Yes, Disable
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Delete Confirm */}
        {showRemoveConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-60 flex items-center justify-center z-50">
            <div className="bg-white p-8 rounded-xl shadow-2xl text-center max-w-md mx-4">
              <div className="mb-6">
                <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 mb-4">
                  <svg className="h-8 w-8 text-red-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 15.5c-.77.833.192 2.5 1.732 2.5z" />
                  </svg>
                </div>
                <h3 className="text-xl font-bold text-gray-900 mb-2">Confirm Deletion</h3>
                <p className="text-gray-600 mb-2">
                  Are you sure you want to permanently delete
                </p>
                <p className="text-lg font-semibold text-[#ED1C24] mb-2">
                  {formData.name}
                </p>
                <p className="text-sm text-gray-500">
                  This action cannot be undone. All data associated with this faculty member will be permanently removed.
                </p>
              </div>
              <div className="flex justify-center gap-4">
                <button
                  onClick={() => setShowRemoveConfirm(false)}
                  className="px-6 py-2 bg-gray-200 text-gray-800 rounded-lg hover:bg-gray-300 transition-colors font-medium"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDelete}
                  className="px-6 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
                >
                  Yes, Delete Permanently
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default EditAddFacultyModal;
