import React, { useState, useEffect } from "react";
import Navbar from "../../common_components/Navbar";
import Sidebar from "../../common_components/Sidebar";
import DepartmentManagement from "../components/DepartmentManagement";
import FacultySection from "../components/FacultySection";
import CISSection from "../components/CISSection";
import type { CISCoord } from "../components/CISSection";
import CCSection from "../components/CCSection";
import HoDSection from "../components/HoDSection";
import type { HodCoord } from "../components/HoDSection";
import EicPrSection from "../components/EicPrSection";
import departmentService from "../../services/departmentService";
import facultyService from "../../services/facultyService";
import cisCoordinatorService from "../../services/cisCoordinatorService";
import { hodService } from "../../services/hodService";
import adminCourseCoordinatorService from "../../services/adminCourseCoordinatorService";
import eicService from "../../services/eicService";
import principalService from "../../services/principalService";
import { academicYearService } from "../../services/academicYearService";

// Initial fallback departments (will be replaced by API data)
const initialDepartments = [
  "Computer Engineering",
  "Information Technology",
  "Electronics & Telecommunication",
  "Artificial Intelligence & Data Science",
  "Mechanical Engineering",
];

// Academic years will be fetched from backend
// const [academicYears, setAcademicYears] = useState<string[]>([]);

const yearSem = [
  "FY - I",
  "FY - II",
  "SY - III",
  "SY - IV",
  "TY - V",
  "TY - VI",
  "LY - VII",
  "LY - VIII",
];

const generateSecurePassword = (length = 8): string => {
  const minLength = Math.max(length, 8);
  const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
  const lowercase = "abcdefghijklmnopqrstuvwxyz";
  const digits = "0123456789";
  const special = "!@#$%^&*()_+-=[]{}|;:,.<>?";
  const all = uppercase + lowercase + digits + special;

  const getRandom = (chars: string) => chars[Math.floor(Math.random() * chars.length)];

  let password = [
    getRandom(uppercase),
    getRandom(digits),
    getRandom(special),
    getRandom(lowercase),
  ];

  for (let i = password.length; i < minLength; i++) {
    password.push(getRandom(all));
  }

  return password.sort(() => Math.random() - 0.5).join("");
};

const AdDashboard: React.FC = () => {
  const roleOptions = [
    { label: "Faculty", value: "faculty" },
    { label: "CIS Coordinator", value: "cis" },
    { label: "Course Coordinator", value: "cc" },
    { label: "Head of Department (HoD)", value: "hod" },
    { label: "Exam Cell (EIC)", value: "eic" },
    { label: "Principal", value: "principal" },
  ];

  const [facultyData, setFacultyData] = useState<any[]>([]);

  // Add loading states
  const [facultyLoading, setFacultyLoading] = useState(true);
  const [cisLoading, setCisLoading] = useState(true);
  const [hodLoading, setHodLoading] = useState(true);
  const [ccLoading, setCcLoading] = useState(true);
  const [eicLoading, setEicLoading] = useState(true);
  const [principalLoading, setPrincipalLoading] = useState(true);

  const [cisData, setCisData] = useState<CISCoord[]>([]);

  const [ccData, setCcData] = useState<any[]>([]);

  const [hodData, setHodData] = useState<HodCoord[]>([]);

  const [eicData, setEicData] = useState({
    email: "<EMAIL>",
    username: "examcelleic",
    savedPassword: "",
    hasPassword: false,
    exists: false,
  });

  const [principalData, setPrincipalData] = useState({
    email: "<EMAIL>",
    username: "principalkjsse",
    savedPassword: "",
    hasPassword: false,
    exists: false,
  });

  const [academicYears, setAcademicYears] = useState<string[]>([]);

  const getNextFacultyId = (role = "Faculty") => {
    // Determine prefix based on role
    let prefix = "FAC";
    if (role === "CIS Coordinator") {
      prefix = "CIS";
    } else if (role === "HOD") {
      prefix = "HOD";
    } else if (role === "Course Coordinator") {
      prefix = "CC";
    } else if (role === "Principal") {
      prefix = "PRI";
    } else if (role === "Admin") {
      prefix = "ADM";
    } else if (role === "Exam Cell") {
      prefix = "EXM";
    }

    const allIds = new Set([
      ...facultyData.map((f) => f.id),
      ...ccData.map((c) => c.id),
      ...disabledIds,
    ]);

    let nextId = 1;
    while (true) {
      const candidate = `${prefix}${nextId.toString().padStart(4, "0")}`;
      if (!allIds.has(candidate)) return candidate;
      nextId++;
    }
  };

  const [filters, setFilters] = useState({
    selectedDepartment: "", // Will be set when departments load
    academicYear: "", // Will be set when academic years load
    role: "faculty",
    yearSem: { cis: "All", cc: "All" },
  });

  const [disabledIds, setDisabledIds] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [isEditDeptOpen, setIsEditDeptOpen] = useState(false);
  const [departmentList, setDepartmentList] = useState<string[]>([]);
  const [departmentsLoading, setDepartmentsLoading] = useState(true);

  // Fetch departments and faculty on component mount
  useEffect(() => {
    const loadDepartments = async () => {
      try {
        setDepartmentsLoading(true);
        const result = await departmentService.getDepartmentNames();
        setDepartmentList(result.data);

        // Set default selected department if not already set
        if (result.data.length > 0 && !result.data.includes(filters.selectedDepartment)) {
          setFilters((prev) => ({ ...prev, selectedDepartment: result.data[0] }));
        }
      } catch (error) {
        console.error("Failed to load departments:", error);
        // Fallback to initial departments
        setDepartmentList(initialDepartments);
      } finally {
        setDepartmentsLoading(false);
      }
    };

    const loadAcademicYears = async () => {
      try {
        const academicYearsData = await academicYearService.getAllAcademicYears();
        setAcademicYears(academicYearsData);

        // Set default academic year to current
        const currentYear = await academicYearService.getCurrentAcademicYear();
        if (currentYear) {
          setFilters(prev => ({ ...prev, academicYear: currentYear }));
        } else if (academicYearsData.length > 0) {
          // Fallback to first available academic year
          setFilters(prev => ({ ...prev, academicYear: academicYearsData[0] }));
        }
      } catch (error) {
        console.error("Failed to load academic years:", error);
        // Fallback to default values
        const fallbackYears = ["2025-2026", "2024-2025", "2023-2024", "2022-2023", "2021-2022"];
        setAcademicYears(fallbackYears);
        setFilters(prev => ({ ...prev, academicYear: fallbackYears[0] }));
      }
    };

    const loadFaculty = async () => {
      try {
        setFacultyLoading(true);
        const facultyList = await facultyService.getFacultyList();
        
        // Transform API data to match existing structure
        const transformedFaculty = facultyList.map((faculty, index) => ({
          id: faculty.facultyID || `F${(index + 1).toString().padStart(4, '0')}`,
          _id: faculty._id, // Keep the MongoDB ID for backend operations
          name: faculty.name || '',
          dob: faculty.dateOfBirth ? new Date(faculty.dateOfBirth).toLocaleDateString('en-GB') : "12-01-1995",
          mobile: faculty.mobileNumber?.replace(/^\+91\s*/, '') || "1234567890",
          email: faculty.email || '',
          department: faculty.progDept || "Computer Engineering",
          username: "", // Will be set when assigned
          password: generateSecurePassword(),
        }));

        setFacultyData(transformedFaculty);
      } catch (error) {
        console.error("Failed to load faculty:", error);
        // Keep fallback data if API fails
      } finally {
        setFacultyLoading(false);
      }
    };

    loadDepartments();
    loadAcademicYears();
    loadFaculty();
    loadEICData();
    loadPrincipalData();
  }, []);

  // Load EIC data
  const loadEICData = async () => {
    try {
      setEicLoading(true);
      const eicInfo = await eicService.getEIC();
      
      setEicData({
        email: eicInfo.email,
        username: eicInfo.username,
        savedPassword: eicInfo.hasPassword ? "***SAVED***" : "",
        hasPassword: eicInfo.hasPassword,
        exists: eicInfo.exists,
      });
    } catch (error) {
      console.error("Failed to load EIC data:", error);
      // Keep default data if API fails
    } finally {
      setEicLoading(false);
    }
  };

  // Load Principal data
  const loadPrincipalData = async () => {
    try {
      setPrincipalLoading(true);
      const principalInfo = await principalService.getPrincipal();
      
      setPrincipalData({
        email: principalInfo.email,
        username: principalInfo.username,
        savedPassword: principalInfo.hasPassword ? "***SAVED***" : "",
        hasPassword: principalInfo.hasPassword,
        exists: principalInfo.exists,
      });
    } catch (error) {
      console.error("Failed to load Principal data:", error);
      // Keep default data if API fails
    } finally {
      setPrincipalLoading(false);
    }
  };

  const loadCISData = async () => {
    try {
      setCisLoading(true);

      // Ensure academic year is set before making API call
      if (!filters.academicYear) {
        console.warn("Academic year not set, skipping CIS data load");
        return;
      }

      // First, try to fetch existing CIS coordinators
      const cisCoordinators = await cisCoordinatorService.getCISCoordinators(filters.academicYear);
      
      // Create a map of department to coordinator for existing ones
      const coordinatorMap: { [key: string]: any } = {};
      cisCoordinators.forEach(coordinator => {
        if (coordinator.department) {
          coordinatorMap[coordinator.department] = coordinator;
        }
      });
      
      // Check if we need to create credentials for any departments that don't have them
      const departmentsNeedingCredentials = departmentList.filter(department => !coordinatorMap[department]);
      
      if (departmentsNeedingCredentials.length > 0) {
        console.log("Creating CIS credentials for departments:", departmentsNeedingCredentials);
        try {
          // Only create credentials for departments that don't have them
          await cisCoordinatorService.createCISCredentials({
            departments: departmentsNeedingCredentials,
            academicYear: filters.academicYear
          });
          
          // Re-fetch CIS coordinators after creation
          const updatedCoordinators = await cisCoordinatorService.getCISCoordinators(filters.academicYear);
          updatedCoordinators.forEach(coordinator => {
            if (coordinator.department) {
              coordinatorMap[coordinator.department] = coordinator;
            }
          });
        } catch (createError) {
          console.warn("Some CIS credentials may already exist:", createError);
          // Don't throw error, just log it and continue with existing data
        }
      }
      
      // Transform to match the expected format - each department has its own CIS coordinator
      const transformedCIS: CISCoord[] = [];
      
      departmentList.forEach(department => {
        const coordinator = coordinatorMap[department];
        // Check if coordinator exists and has a real name (not "Unassigned")
        const hasRealAssignment = coordinator && coordinator.name && coordinator.name !== "Unassigned";
        transformedCIS.push({
          academicYear: filters.academicYear,
          department: department,
          name: hasRealAssignment ? coordinator.name : "", // Empty string means unassigned
          username: coordinator ? coordinator.email.split('@')[0] : "",
          email: coordinator ? coordinator.email : `cis${department.toLowerCase().replace(/\s+/g, '')}@somaiya.edu`,
          password: "", // Don't expose password in frontend
        });
      });

      console.log("Transformed CIS data:", transformedCIS);
      setCisData(transformedCIS);
    } catch (error) {
      console.error("Failed to load CIS data:", error);
      // Keep empty array if API fails
      setCisData([]);
    } finally {
      setCisLoading(false);
    }
  };

  // Load HoD data
  const loadHoDData = async () => {
    try {
      setHodLoading(true);

      // Ensure academic year is set before making API call
      if (!filters.academicYear) {
        console.warn("Academic year not set, skipping HoD data load");
        return;
      }

      // First, try to get existing HoDs from backend
      console.log("Fetching HoDs for academic year:", filters.academicYear);
      const existingHoDs = await hodService.getHoDs(filters.academicYear);
      console.log("Existing HoDs from backend:", existingHoDs);
      
      // Create a map of department to HoD for existing ones
      const hodMap: { [key: string]: any } = {};
      existingHoDs.forEach(hod => {
        if (hod.department) {
          hodMap[hod.department] = hod;
        }
      });
      
      // Check if we need to create credentials for any departments that don't have them
      const departmentsNeedingCredentials = departmentList.filter(department => !hodMap[department]);
      
      if (departmentsNeedingCredentials.length > 0) {
        console.log("Creating HoD credentials for departments:", departmentsNeedingCredentials);
        try {
          // Only create credentials for departments that don't have them
          const result = await hodService.createHoDCredentials({
            departments: departmentsNeedingCredentials,
            academicYear: filters.academicYear
          });
          console.log("HoD credentials created:", result);
          
          // Re-fetch HoDs after creation
          const updatedHoDs = await hodService.getHoDs(filters.academicYear);
          updatedHoDs.forEach(hod => {
            if (hod.department) {
              hodMap[hod.department] = hod;
            }
          });
        } catch (createError) {
          console.warn("Some HoD credentials may already exist:", createError);
          // Don't throw error, just log it and continue with existing data
        }
      }
      
      // Transform to match the expected format - each department has its own HoD
      const transformedHoDs: any[] = [];
      
      departmentList.forEach(department => {
        const hod = hodMap[department];
        // Check if HoD exists and has a real name (not "Unassigned")
        const hasRealAssignment = hod && hod.name && hod.name !== "Unassigned";
        transformedHoDs.push({
          academicYear: filters.academicYear,
          department: department,
          name: hasRealAssignment ? hod.name : "", // Empty string means unassigned
          username: hod ? hod.email.split('@')[0] : "",
          email: hod ? hod.email : `hod${department.toLowerCase().replace(/\s+/g, '')}@somaiya.edu`,
          password: "", // Don't expose password in frontend
        });
      });
      
      // Update HoD data with the results
      setHodData(transformedHoDs);
      console.log("HoD data has been set to:", transformedHoDs);
    } catch (error) {
      console.error("Error loading HoD data:", error);
      // Don't throw the error, just log it and continue
    } finally {
      setHodLoading(false);
    }
  };

  // Load CIS data when departments are loaded and academic year is set
  useEffect(() => {
    if (departmentList.length > 0 && !departmentsLoading && filters.academicYear) {
      loadCISData();
    }
  }, [departmentList, departmentsLoading, filters.academicYear]);

  // Load HoD data when departments are loaded and academic year is set
  useEffect(() => {
    if (departmentList.length > 0 && !departmentsLoading && filters.academicYear) {
      loadHoDData();
    }
  }, [departmentList, departmentsLoading, filters.academicYear]);

  // Load CC data when departments are loaded and academic year is set
  useEffect(() => {
    console.log("🔄 [UseEffect] CC data dependency change detected:", {
      departmentList: departmentList.length,
      departmentsLoading,
      academicYear: filters.academicYear
    });

    if (departmentList.length > 0 && !departmentsLoading && filters.academicYear) {
      console.log("✅ [UseEffect] Conditions met, calling loadCCData");
      loadCCData();
    } else {
      console.log("⏳ [UseEffect] Conditions not met:", {
        departmentListEmpty: departmentList.length === 0,
        departmentsLoading,
        academicYearEmpty: !filters.academicYear
      });
    }
  }, [departmentList, departmentsLoading, filters.academicYear]);

  // Load CC data function
  const loadCCData = async () => {
    try {
      console.log("🚀 Starting loadCCData function");
      console.log("📊 Current state:", {
        departmentList,
        academicYear: filters.academicYear,
        departmentListLength: departmentList.length
      });

      // Ensure academic year is set before making API call
      if (!filters.academicYear) {
        console.warn("Academic year not set, skipping CC data load");
        return;
      }

      setCcLoading(true);

      // First, try to get existing Course Coordinators from backend
      console.log("📡 Fetching Course Coordinators for academic year:", filters.academicYear);
      const existingCCs = await adminCourseCoordinatorService.getCourseCoordinators(filters.academicYear);
      console.log("✅ Existing Course Coordinators from backend:", existingCCs);
      console.log("📈 Number of existing CCs:", existingCCs.length);
      
      // Set the CC data from existing assignments
      setCcData(existingCCs);
      console.log("💾 CC data has been set to:", existingCCs);
      
      // Check if we need to create credentials for any courses
      // Only proceed with creation if there are very few or no existing CCs
      if (existingCCs.length === 0 && departmentList.length > 0) {
        console.log("🔍 No existing CCs found, checking course data...");
        
        try {
          const debugResult = await adminCourseCoordinatorService.debugCourseData();
          console.log("� Debug result:", debugResult);
          
          // If no courses exist, seed some sample data
          if (debugResult.total === 0) {
            console.log("🌱 No courses found, seeding sample data...");
            const seedResult = await adminCourseCoordinatorService.seedCourseData();
            console.log("✅ Seed result:", seedResult);
          }
          
          // Now create credentials for departments
          console.log("🏗️ Creating CC credentials for departments:", departmentList);
          const result = await adminCourseCoordinatorService.createCredentials({
            departments: departmentList,
            academicYear: filters.academicYear
          });
          console.log("✅ CC credentials created successfully:", result);
          console.log("📋 Created credentials count:", result.credentials?.length || 0);
          
          // Reload Course Coordinators after creating credentials
          console.log("🔄 Reloading Course Coordinators after creation...");
          const updatedCCs = await adminCourseCoordinatorService.getCourseCoordinators(filters.academicYear);
          console.log("✅ Updated Course Coordinators:", updatedCCs);
          console.log("📈 Final CC count:", updatedCCs.length);
          setCcData(updatedCCs);
        } catch (createError) {
          console.error("❌ Error creating CC credentials:", createError);
          console.log("🔍 Create error details:", {
            message: (createError as any)?.message,
            status: (createError as any)?.response?.status,
            data: (createError as any)?.response?.data
          });
        }
      } else {
        console.log("ℹ️ Skipping credential creation - existing CCs found or no departments");
      }
    } catch (error) {
      console.error("❌ Error in loadCCData:", error);
      console.log("🔍 Main error details:", {
        message: (error as any)?.message,
        stack: (error as any)?.stack,
        response: (error as any)?.response
      });
      setCcData([]);
    } finally {
      console.log("🏁 Finishing loadCCData function");
      setCcLoading(false);
    }
  };

  // Handle EIC password save
  const handleEICPasswordSave = async (newPassword: string) => {
    try {
      if (eicData.exists) {
        // Update existing EIC password
        await eicService.updateEICPassword(newPassword);
      } else {
        // Create new EIC with password
        await eicService.createOrUpdateEIC({
          email: eicData.email,
          username: eicData.username,
          password: newPassword
        });
      }
      
      setEicData((prev) => ({
        ...prev,
        savedPassword: "***SAVED***",
        hasPassword: true,
        exists: true,
      }));
    } catch (error) {
      console.error("Failed to save EIC password:", error);
      // You might want to show an error notification here
    }
  };

  // Handle Principal password save
  const handlePrincipalPasswordSave = async (newPassword: string) => {
    try {
      if (principalData.exists) {
        // Update existing Principal password
        await principalService.updatePrincipalPassword(newPassword);
      } else {
        // Create new Principal with password
        await principalService.createOrUpdatePrincipal({
          email: principalData.email,
          username: principalData.username,
          password: newPassword
        });
      }
      
      setPrincipalData((prev) => ({
        ...prev,
        savedPassword: "***SAVED***",
        hasPassword: true,
        exists: true,
      }));
    } catch (error) {
      console.error("Failed to save Principal password:", error);
      // You might want to show an error notification here
    }
  };

  const handleDepartmentsUpdate = (updatedDepartments: string[]) => {
    setDepartmentList(updatedDepartments);

    // Update related data to only include valid departments
    setFacultyData((prev) => prev.filter((f) => updatedDepartments.includes(f.department)));
    setCcData((prev) => prev.filter((cc) => updatedDepartments.includes(cc.department)));
    setCisData((prev) => prev.filter((cis) => updatedDepartments.includes(cis.department)));
    setHodData((prev) => prev.filter((h) => updatedDepartments.includes(h.department)));
    setHodData((prev) => prev.filter((h) => updatedDepartments.includes(h.department)));

    // Update selected department if it's no longer valid
    if (!updatedDepartments.includes(filters.selectedDepartment)) {
      setFilters((prev) => ({ ...prev, selectedDepartment: updatedDepartments[0] || "" }));
    }
  };

  const handleFilterChange = (key: string, value: any) =>
    setFilters((prev) => ({ ...prev, [key]: value }));

  const updateCoordName = (data: any[], name: string) =>
    data.map((d) => (d.name?.toLowerCase() === name ? { ...d, name: "" } : d));

  const handleDisableOrDelete = async (id: string, deleteFlag = false, enableFlag = false) => {
    const target = facultyData.find((f) => f.id === id);
    if (!target) return;

    const normalized = target.name.trim().toLowerCase();

    try {
      // Use _id for backend operations, fallback to id if _id not available
      const backendId = (target as any)._id || target.id;
      
      if (deleteFlag) {
        // Call backend to delete faculty
        await facultyService.deleteFaculty(backendId);
        setFacultyData((prev) => prev.filter((f) => f.id !== id));
        setDisabledIds((prev) => prev.filter((d) => d !== id));
      } else if (enableFlag) {
        // Call backend to enable faculty
        await facultyService.toggleFacultyStatus(backendId, true);
        setDisabledIds((prev) => prev.filter((d) => d !== id));
      } else {
        // Call backend to disable faculty
        await facultyService.toggleFacultyStatus(backendId, false);
        setDisabledIds((prev) => [...prev, id]);
      }

      // Update related coordinator data
      setCcData((prev) => prev.filter((cc) => cc.id !== id));
      setCisData((prev) => updateCoordName(prev, normalized));
      setHodData((prev) => updateCoordName(prev, normalized));
    } catch (error) {
      console.error("Error updating faculty status:", error);
      alert("Error updating faculty: " + (error as Error).message);
    }
  };

  const filteredFaculty = facultyData.filter(
    (f) => filters.selectedDepartment === "All" || f.department === filters.selectedDepartment
  );

  const filteredCC = ccData.filter(
    (cc) =>
      !disabledIds.includes(cc.id) &&
      (filters.selectedDepartment === "All" || cc.department === filters.selectedDepartment) &&
      (filters.academicYear === "All" || cc.academicYear === filters.academicYear) &&
      (filters.yearSem.cc === "All" || cc.yearSem === filters.yearSem.cc)
  );

  const activeFaculty = facultyData.filter((f) => !disabledIds.includes(f.id));

  return (
    <div className="flex flex-col h-screen">
      <Navbar role="admin" />
      <div className="flex flex-1 overflow-hidden">
        <div className="hidden md:block">
          <Sidebar role="admin" />
        </div>

        <main className="flex-1 py-6 px-1.5 md:px-4 bg-white overflow-auto">
          <h1 className="text-[27px] font-bold text-[#ED1C24] text-center mb-7">Dashboard</h1>

          <div className="flex flex-wrap sm:flex-nowrap justify-between items-center gap-4 px-3 pb-4 border-b shadow-sm mb-4">
            {/* Role Select */}
            {filters.role === "cc" ? (
              <div className="flex gap-4">
                <div className="flex flex-col w-[260px]">
                  <label className="text-[16px] font-medium mb-1">Role:</label>
                  <select
                    className="border px-3 py-1.5 rounded shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                    value={filters.role}
                    onChange={(e) => handleFilterChange("role", e.target.value)}
                  >
                    {roleOptions.map((role) => (
                      <option key={role.value} value={role.value}>
                        {role.label}
                      </option>
                    ))}
                  </select>
                </div>

                <div className="flex flex-col w-[400px]">
                  <label className="text-[16px] font-medium mb-1">Department:</label>
                  <select
                    className="border px-4 py-1.5 rounded w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                    value={filters.selectedDepartment}
                    onChange={(e) => handleFilterChange("selectedDepartment", e.target.value)}
                    disabled={departmentsLoading}
                  >
                    {departmentsLoading ? (
                      <option>Loading departments...</option>
                    ) : departmentList.length === 0 ? (
                      <option>No departments available</option>
                    ) : (
                      departmentList.map((d) => (
                        <option key={d} value={d}>
                          {d}
                        </option>
                      ))
                    )}
                  </select>
                </div>
              </div>
            ) : (
              // Default layout for other roles
              <>
                <div className="flex flex-col w-[260px]">
                  <label className="text-[16px] font-medium mb-1">Role:</label>
                  <select
                    className="border px-3 py-1.5 rounded shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                    value={filters.role}
                    onChange={(e) => handleFilterChange("role", e.target.value)}
                  >
                    {roleOptions.map((role) => (
                      <option key={role.value} value={role.value}>
                        {role.label}
                      </option>
                    ))}
                  </select>
                </div>

                {filters.role === "faculty" && (
                  <div className="flex flex-col w-[400px] mt-4">
                    <label className="text-[16px] font-medium mb-1">Department:</label>
                    <div className="flex gap-2">
                      <select
                        className="border px-4 py-1.5 rounded w-full shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                        value={filters.selectedDepartment}
                        onChange={(e) => handleFilterChange("selectedDepartment", e.target.value)}
                        disabled={departmentsLoading}
                      >
                        {departmentsLoading ? (
                          <option>Loading departments...</option>
                        ) : departmentList.length === 0 ? (
                          <option>No departments available</option>
                        ) : (
                          departmentList.map((d) => (
                            <option key={d} value={d}>
                              {d}
                            </option>
                          ))
                        )}
                      </select>
                      <button
                        onClick={() => setIsEditDeptOpen(true)}
                        className="bg-[#ED1C24] text-white rounded px-3 py-1.5 hover:bg-red-800"
                        title="Edit departments"
                      >
                        Edit
                      </button>
                    </div>
                  </div>
                )}
              </>
            )}

            {["cc"].includes(filters.role) && (
              <div className="flex flex-col w-full sm:w-[180px]">
                <label className="text-[16px] font-medium mb-1">Academic Year:</label>
                <select
                  className="border rounded px-4 py-1.5 shadow-[0_2px_3px_rgba(0,0,0,0.15)]"
                  value={filters.academicYear}
                  onChange={(e) => handleFilterChange("academicYear", e.target.value)}
                >
                  {academicYears.map((year) => (
                    <option key={year}>{year}</option>
                  ))}
                </select>
              </div>
            )}
          </div>

          {/* Dynamic Section */}
          <div className="p-4 shadow-md">
            {filters.role === "faculty" && (
              <FacultySection
                getNextFacultyId={getNextFacultyId}
                generateSecurePassword={generateSecurePassword}
                searchQuery={searchQuery}
                onSearchQueryChange={setSearchQuery}
                facultyData={filteredFaculty}
                setFacultyData={setFacultyData}
                disabledIds={disabledIds}
                onDisableFaculty={(id, enable = false) => handleDisableOrDelete(id, false, enable)}
                onDeleteFaculty={(id) => handleDisableOrDelete(id, true)}
                departments={departmentList}
                isLoading={facultyLoading}
              />
            )}

            {filters.role === "cis" && (
              <CISSection
                academicYear={filters.academicYear}
                cisData={cisData.filter((c) => c.academicYear === filters.academicYear)}
                setCisData={(newData) => {
                  const otherYears = cisData.filter((c) => c.academicYear !== filters.academicYear);
                  setCisData([...otherYears, ...newData]);
                }}
                facultyData={activeFaculty}
                department={departmentList}
                isLoading={cisLoading}
              />
            )}

            {filters.role === "cc" && (
              <CCSection
                academicYear={filters.academicYear}
                selectedDepartment={filters.selectedDepartment}
                ccData={ccData}
                setCcData={setCcData}
                facultyData={activeFaculty}
                isLoading={ccLoading}
                yearSemester={filters.yearSem.cc}
                yearSemeOptions={["All", ...yearSem]}
                onYearSemesterChange={(val) =>
                  handleFilterChange("yearSem", { ...filters.yearSem, cc: val })
                }
                searchQuery={searchQuery}
                onSearchQueryChange={setSearchQuery}
                getFilteredCc={() => filteredCC}
              />
            )}

            {filters.role === "hod" && (
              <HoDSection
                academicYear={filters.academicYear}
                hodData={hodData.filter((h) => h.academicYear === filters.academicYear)}
                setHodData={(newData) => {
                  const otherYears = hodData.filter((h) => h.academicYear !== filters.academicYear);
                  setHodData([...otherYears, ...newData]);
                }}
                facultyData={activeFaculty}
                department={departmentList}
                isLoading={hodLoading}
              />
            )}

            {filters.role === "eic" && (
              <EicPrSection
                label="Exam Cell (EIC)"
                username={eicData.username}
                email={eicData.email}
                savedPassword={eicData.savedPassword}
                hasPassword={eicData.hasPassword}
                onSavePassword={handleEICPasswordSave}
              />
            )}

            {filters.role === "principal" && (
              <EicPrSection
                label="Principal"
                username={principalData.username}
                email={principalData.email}
                savedPassword={principalData.savedPassword}
                hasPassword={principalData.hasPassword}
                onSavePassword={handlePrincipalPasswordSave}
              />
            )}
          </div>

          {/* Department Management Modal */}
          {isEditDeptOpen && (
            <DepartmentManagement
              onClose={() => setIsEditDeptOpen(false)}
              onDepartmentsUpdate={handleDepartmentsUpdate}
            />
          )}
        </main>
      </div>
    </div>
  );
};

export default AdDashboard;
